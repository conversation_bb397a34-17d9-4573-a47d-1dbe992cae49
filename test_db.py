import sys
sys.path.append('backend')

from flask import Flask
from models import *
from database import db

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = (
    'mssql+pyodbc://localhost/OnlineJudge?trusted_connection=yes&driver=ODBC+Driver+17+for+SQL+Server&trusted_connection=yes&TrustServerCertificate=yes')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

app.config['DB_CONNECTION_POOLS'] = {
    'default': {
        'host': 'localhost',
        'port': 1433,
        'user': 'admin',
        'password': 'admin',
        'database': 'OnlineJudge',
        'uri': 'mssql+pyodbc://localhost/OnlineJudge?trusted_connection=yes&driver=ODBC+Driver+17+for+SQL+Server&trusted_connection=yes&TrustServerCertificate=yes',
        'pool_size': 5,
        'max_overflow': 10
    }
}

db.init_app(app)

with app.app_context():
    try:
        print("测试数据库连接...")
        count = Questions.query.count()
        print(f"数据库连接成功，共有 {count} 道题目")

        # 获取前5道题目
        questions = Questions.query.limit(5).all()
        print("\n前5道题目:")
        for q in questions:
            print(f"ID: {q.QuesID}, 标题: {q.QuesName}, 难度: {q.QuesLevel}, 类型: {q.QuesType}")

    except Exception as e:
        print(f"数据库连接失败: {e}")
        import traceback
        traceback.print_exc()

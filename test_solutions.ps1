# 测试题解管理API
Write-Host "测试题解管理API..."

# 测试获取题解列表
Write-Host "1. 测试获取题解列表..."
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/solutions?pg=1&ppg=10" -Method GET -Headers @{'Authorization'='Bearer test_token'}
    Write-Host "✅ 获取题解列表测试: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "❌ 获取题解列表测试失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

Write-Host ""

# 测试创建题解
Write-Host "2. 测试创建题解..."
$createBody = @{
    AnsTitle = "测试题解"
    ProblemID = 73
    Content = "这是一个测试题解的内容"
    Status = "pending"
    UserID = 1
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/solutions?action=update_solutions" -Method POST -Headers @{'Authorization'='Bearer test_token'; 'Content-Type'='application/json'} -Body $createBody
    Write-Host "✅ 创建题解测试: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "❌ 创建题解测试失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

Write-Host ""

# 测试删除题解（使用不存在的ID）
Write-Host "3. 测试删除不存在的题解..."
$deleteBody = @{
    ans_id = 999999
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/solutions?action=remove_solutions" -Method POST -Headers @{'Authorization'='Bearer test_token'; 'Content-Type'='application/json'} -Body $deleteBody
    Write-Host "✅ 删除题解测试: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "❌ 删除题解测试失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

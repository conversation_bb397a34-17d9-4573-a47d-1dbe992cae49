<template>
  <div class="login-wrapper">
    <div class="login-box">
      <h2 class="login-title">欢迎登录</h2>
      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label for="username">用户名</label>
          <input
            type="text"
            id="username"
            v-model="username"
            placeholder="请输入用户名"
            required
            :disabled="isLoading"
          />
        </div>
        <div class="form-group">
          <label for="password">密码</label>
          <input
            type="password"
            id="password"
            v-model="password"
            placeholder="请输入密码"
            required
            :disabled="isLoading"
          />
        </div>
        <button type="submit" class="btn-login" :disabled="isLoading">
          <span v-if="!isLoading">登录</span>
          <span v-else class="loading-spinner"></span>
        </button>
      </form>

      <p v-if="errorMessage" class="error-msg">{{ errorMessage }}</p>

      <p class="to-register">
        还没有账号？
        <button @click="goToRegister" class="btn-link" :disabled="isLoading">
          立即注册
        </button>
      </p>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";

const username = ref("");
const password = ref("");
const errorMessage = ref("");
const isLoading = ref(false);
const router = useRouter();

async function handleLogin() {
  // 清空错误信息
  errorMessage.value = "";

  // 验证输入
  if (!username.value.trim() || !password.value.trim()) {
    errorMessage.value = "用户名和密码不能为空";
    return;
  }

  // 设置加载状态
  isLoading.value = true;

  try {
    // ------------------------------预设账号逻辑 - 仅用于开发测试-----------------------------------
    if (process.env.NODE_ENV === "development") {
      // 预设普通用户
      if (username.value === "user" && password.value === "user") {
        // 模拟成功登录响应
        localStorage.setItem("token", "mock-user-token");
        localStorage.setItem(
          "userInfo",
          JSON.stringify({
            id: 999,
            username: "user",
            userType: "user",
          })
        );
        router.push("/welcome");
        return;
      }

      // 预设管理员
      if (username.value === "admin" && password.value === "admin") {
        // 模拟成功登录响应
        localStorage.setItem("token", "mock-admin-token");
        localStorage.setItem(
          "userInfo",
          JSON.stringify({
            id: 888,
            username: "admin",
            userType: "admin",
          })
        );
        router.push("/admin");
        return;
      }
    }

    // 正常的API登录逻辑
    const res = await fetch("http://127.0.0.1:5000/api/login", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        username: username.value,
        password: password.value,
      }),
    });

    const data = await res.json();

    if (res.ok) {
      // 存储token
      localStorage.setItem("token", data.token);

      // 存储用户信息，包括类型
      localStorage.setItem(
        "userInfo",
        JSON.stringify({
          id: data.user.id,
          username: data.user.username,
          userType: data.user.type || "user", // 存储用户类型，默认为普通用户
        })
      );

      // 根据用户类型跳转到不同页面
      if (data.user.type === "admin") {
        router.push("/admin"); // 管理员跳转到管理面板
      } else {
        router.push("/welcome"); // 普通用户跳转到欢迎页面
      }
    } else {
      errorMessage.value = data.error || "用户名或密码错误";
    }
  } catch (err) {
    errorMessage.value = "服务器连接失败，请稍后重试";
  } finally {
    // 无论成功失败，都结束加载状态
    isLoading.value = false;
  }
}

function goToRegister() {
  router.push("/register");
}
</script>

<style scoped>
.login-wrapper {
  height: 100vh;
  background: linear-gradient(135deg, #11cb21, #25c3fc);
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.login-box {
  background: #fff;
  padding: 2.5rem 3rem;
  border-radius: 12px;
  width: 360px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  text-align: center;
}

.login-title {
  margin-bottom: 1.8rem;
  font-weight: 700;
  font-size: 1.8rem;
  color: #333;
}

.form-group {
  margin-bottom: 1.4rem;
  text-align: left;
}

label {
  display: block;
  margin-bottom: 0.4rem;
  font-weight: 600;
  color: #555;
}

input[type="text"],
input[type="password"] {
  width: 100%;
  padding: 0.55rem 1rem;
  border: 1.8px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

input[type="text"]:focus,
input[type="password"]:focus {
  border-color: #2575fc;
  outline: none;
  box-shadow: 0 0 5px rgba(37, 117, 252, 0.5);
}

.btn-login {
  width: 100%;
  background-color: #2575fc;
  color: #fff;
  padding: 0.65rem;
  font-size: 1.1rem;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-login:hover {
  background-color: #1a52d8;
}

.btn-login:disabled {
  background-color: #6c9bd8;
  cursor: not-allowed;
}

.error-msg {
  margin-top: 1rem;
  color: #e74c3c;
  font-weight: 600;
  font-size: 0.95rem;
}

.to-register {
  margin-top: 1rem;
  font-size: 0.9rem;
  color: #555;
  text-align: center;
}

.btn-link {
  background: none;
  border: none;
  color: #2575fc;
  cursor: pointer;
  padding: 0;
  font-weight: 600;
}

.btn-link:hover {
  text-decoration: underline;
}

.btn-link:disabled {
  color: #a0b9e0;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}
</style>

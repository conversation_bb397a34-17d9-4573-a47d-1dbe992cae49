<template>
  <div class="problem-management">
    <h2>题目管理</h2>

    <!-- 搜索和筛选区域 -->
    <div class="filter-section">
      <el-input
        v-model="searchQuery"
        placeholder="搜索题目..."
        class="search-input"
        clearable
        @clear="fetchProblems"
        @keyup.enter="searchProblems"
      >
        <template #append>
          <el-button @click="searchProblems">
            <el-icon><Search /></el-icon>
          </el-button>
        </template>
      </el-input>

      <el-select
        v-model="difficultyFilter"
        placeholder="难度"
        clearable
        @change="fetchProblems"
      >
        <el-option label="简单" value="简单" />
        <el-option label="中等" value="中等" />
        <el-option label="困难" value="困难" />
      </el-select>

      <el-select
        v-model="categoryFilter"
        placeholder="分类"
        clearable
        @change="fetchProblems"
      >
        <el-option
          v-for="category in categories"
          :key="category"
          :label="category"
          :value="category"
        />
      </el-select>
    </div>

    <div class="action-bar">
      <el-button type="primary" @click="openProblemDialog()">
        <el-icon><Plus /></el-icon> 新建题目
      </el-button>
      <el-button @click="fetchProblems">
        <el-icon><Refresh /></el-icon> 刷新
      </el-button>
    </div>

    <div class="problem-table-container">
      <el-table :data="problems" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" sortable />
        <el-table-column prop="title" label="题目标题" width="250" />
        <el-table-column prop="difficulty" label="难度" width="100" sortable>
          <template #default="scope">
            <el-tag :type="getDifficultyType(scope.row.difficulty)">
              {{ scope.row.difficulty }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="tags" label="标签" width="200">
          <template #default="scope">
            <el-tag
              v-for="tag in getTagsArray(scope.row.tags)"
              :key="tag"
              size="small"
              class="tag-item"
            >
              {{ tag }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="submitted"
          label="提交次数"
          width="100"
          sortable
        />
        <el-table-column
          prop="accepted"
          label="通过次数"
          width="100"
          sortable
        />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button size="small" @click="openProblemDialog(scope.row)"
              >编辑</el-button
            >
            <el-button
              size="small"
              type="primary"
              @click="viewProblem(scope.row)"
              >查看</el-button
            >
            <el-button
              size="small"
              type="danger"
              @click="deleteProblem(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页控件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalProblems"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 题目表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑题目' : '新建题目'"
      width="70%"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="problemFormRef"
        :model="problemForm"
        :rules="formRules"
        label-width="100px"
        label-position="top"
      >
        <el-form-item label="题目标题" prop="title">
          <el-input v-model="problemForm.title" placeholder="请输入题目标题" />
        </el-form-item>

        <el-form-item label="难度" prop="difficulty">
          <el-select v-model="problemForm.difficulty" placeholder="请选择难度">
            <el-option label="简单" value="简单" />
            <el-option label="中等" value="中等" />
            <el-option label="困难" value="困难" />
          </el-select>
        </el-form-item>

        <el-form-item label="标签" prop="tags">
          <el-select
            v-model="problemForm.tags"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请选择或创建标签"
          >
            <el-option
              v-for="tag in allTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="题目描述" prop="description">
          <el-input
            v-model="problemForm.description"
            type="textarea"
            :rows="6"
            placeholder="请输入题目描述，支持Markdown格式"
          />
        </el-form-item>

        <el-form-item label="输入描述" prop="inputDescription">
          <el-input
            v-model="problemForm.inputDescription"
            type="textarea"
            :rows="3"
            placeholder="请描述输入格式"
          />
        </el-form-item>

        <el-form-item label="输出描述" prop="outputDescription">
          <el-input
            v-model="problemForm.outputDescription"
            type="textarea"
            :rows="3"
            placeholder="请描述输出格式"
          />
        </el-form-item>

        <el-form-item label="示例">
          <div
            v-for="(example, index) in problemForm.examples"
            :key="index"
            class="example-item"
          >
            <div class="example-header">
              <span>示例 {{ index + 1 }}</span>
              <el-button
                type="danger"
                size="small"
                circle
                @click="removeExample(index)"
                v-if="problemForm.examples.length > 1"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>

            <el-form-item label="输入示例">
              <el-input
                v-model="example.input"
                type="textarea"
                :rows="2"
                placeholder="输入示例"
              />
            </el-form-item>

            <el-form-item label="输出示例">
              <el-input
                v-model="example.output"
                type="textarea"
                :rows="2"
                placeholder="输出示例"
              />
            </el-form-item>

            <el-form-item label="说明">
              <el-input
                v-model="example.explanation"
                type="textarea"
                :rows="2"
                placeholder="示例说明（可选）"
              />
            </el-form-item>
          </div>

          <el-button type="primary" plain @click="addExample">
            <el-icon><Plus /></el-icon> 添加示例
          </el-button>
        </el-form-item>

        <el-form-item label="测试用例" prop="testCases">
          <div
            v-for="(testCase, index) in problemForm.testCases"
            :key="index"
            class="test-case-item"
          >
            <div class="test-case-header">
              <span>测试用例 {{ index + 1 }}</span>
              <el-button
                type="danger"
                size="small"
                circle
                @click="removeTestCase(index)"
                v-if="problemForm.testCases.length > 1"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>

            <el-form-item label="输入">
              <el-input
                v-model="testCase.input"
                type="textarea"
                :rows="2"
                placeholder="测试用例输入"
              />
            </el-form-item>

            <el-form-item label="期望输出">
              <el-input
                v-model="testCase.output"
                type="textarea"
                :rows="2"
                placeholder="期望输出"
              />
            </el-form-item>
          </div>

          <el-button type="primary" plain @click="addTestCase">
            <el-icon><Plus /></el-icon> 添加测试用例
          </el-button>
        </el-form-item>

        <el-form-item label="时间限制(ms)" prop="timeLimit">
          <el-input-number
            v-model="problemForm.timeLimit"
            :min="100"
            :max="10000"
            :step="100"
          />
        </el-form-item>

        <el-form-item label="内存限制(MB)" prop="memoryLimit">
          <el-input-number
            v-model="problemForm.memoryLimit"
            :min="16"
            :max="1024"
            :step="16"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitProblemForm"
            :loading="submitting"
          >
            {{ isEdit ? "保存修改" : "创建题目" }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Plus,
  Search,
  Download,
  Refresh,
  Delete,
} from "@element-plus/icons-vue";
import axios from "axios";

const router = useRouter();
const problems = ref([]);
const loading = ref(true);
const submitting = ref(false);
const dialogVisible = ref(false);
const isEdit = ref(false);
const problemFormRef = ref(null);
const searchQuery = ref("");
const difficultyFilter = ref("");
const categoryFilter = ref("");
const currentPage = ref(1);
const pageSize = ref(10);
const totalProblems = ref(0);

// 所有可用的分类和标签
const categories = ref([
  "数组",
  "链表",
  "字符串",
  "树",
  "图",
  "动态规划",
  "贪心",
  "回溯",
  "数学",
  "排序",
  "搜索",
  "位运算",
  "哈希表",
]);
const allTags = ref([
  "数组",
  "链表",
  "字符串",
  "树",
  "图",
  "动态规划",
  "贪心",
  "回溯",
  "数学",
  "排序",
  "搜索",
  "位运算",
  "哈希表",
  "双指针",
  "栈",
  "队列",
  "堆",
  "递归",
  "分治",
  "滑动窗口",
  "前缀和",
  "二分查找",
  "深度优先搜索",
  "广度优先搜索",
]);

// 表单数据
const problemForm = reactive({
  id: null,
  title: "",
  difficulty: "",
  tags: [],
  description: "",
  inputDescription: "",
  outputDescription: "",
  examples: [{ input: "", output: "", explanation: "" }],
  testCases: [{ input: "", output: "" }],
  timeLimit: 1000,
  memoryLimit: 256,
});

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: "请输入题目标题", trigger: "blur" },
    {
      min: 3,
      max: 100,
      message: "标题长度应在3到100个字符之间",
      trigger: "blur",
    },
  ],
  difficulty: [{ required: true, message: "请选择难度", trigger: "change" }],
  description: [{ required: true, message: "请输入题目描述", trigger: "blur" }],
  inputDescription: [
    { required: true, message: "请输入输入描述", trigger: "blur" },
  ],
  outputDescription: [
    { required: true, message: "请输入输出描述", trigger: "blur" },
  ],
  timeLimit: [{ required: true, message: "请设置时间限制", trigger: "blur" }],
  memoryLimit: [{ required: true, message: "请设置内存限制", trigger: "blur" }],
};

// 获取题目列表
async function fetchProblems() {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {
      page: currentPage.value,
      per_page: pageSize.value,
    };

    if (searchQuery.value) {
      params.search = searchQuery.value;
    }

    if (difficultyFilter.value) {
      params.difficulty = difficultyFilter.value;
    }

    if (categoryFilter.value) {
      params.tag = categoryFilter.value;
    }

    // 实际API调用
    console.log("发送的参数:", params);
    const response = await axios.post(
      "http://127.0.0.1:5000/api/questions/filter",
      { params }
    );

    if (response.status === 200) {
      problems.value = response.data.problems;
      totalProblems.value = response.data.total;
    } else {
      throw new Error("获取题目列表失败");
    }
  } catch (error) {
    console.error("获取题目列表失败:", error);

    // 详细的错误处理
    if (error.response) {
      const status = error.response.status;
      const errorMsg =
        error.response.data?.error ||
        error.response.data?.message ||
        "获取题目列表失败";

      if (status === 401) {
        ElMessage.error("登录已过期，请重新登录");
      } else if (status === 403) {
        ElMessage.error("权限不足，需要管理员权限");
      } else {
        ElMessage.error(`获取题目列表失败: ${errorMsg}`);
      }
    } else if (error.request) {
      ElMessage.error("网络错误，请检查服务器连接");
    } else {
      ElMessage.error("获取题目列表失败，请稍后重试");
    }

    // 如果API调用失败，使用模拟数据（仅用于开发）
    problems.value = [
      {
        id: 1,
        title: "两数之和",
        difficulty: "简单",
        tags: ["数组", "哈希表"],
        accepted: 120,
        submitted: 150,
      },
      {
        id: 2,
        title: "链表反转",
        difficulty: "中等",
        tags: ["链表"],
        accepted: 80,
        submitted: 110,
      },
      {
        id: 3,
        title: "最长回文子串",
        difficulty: "困难",
        tags: ["字符串", "动态规划"],
        accepted: 50,
        submitted: 120,
      },
    ];
    totalProblems.value = problems.value.length;
  } finally {
    loading.value = false;
  }
}

// 搜索题目
function searchProblems() {
  currentPage.value = 1; // 重置到第一页
  fetchProblems();
}

// 处理分页大小变化
function handleSizeChange(size) {
  pageSize.value = size;
  fetchProblems();
}

// 处理页码变化
function handleCurrentChange(page) {
  currentPage.value = page;
  fetchProblems();
}

// 根据难度获取标签类型
function getDifficultyType(difficulty) {
  switch (difficulty) {
    case "简单":
      return "success";
    case "中等":
      return "warning";
    case "困难":
      return "danger";
    default:
      return "info";
  }
}

// 打开题目表单对话框
function openProblemDialog(problem = null) {
  resetForm();

  if (problem) {
    // 编辑现有题目
    isEdit.value = true;
    Object.keys(problemForm).forEach((key) => {
      if (key in problem) {
        problemForm[key] = problem[key];
      }
    });

    // 如果没有示例或测试用例，添加默认空项
    if (!problemForm.examples || problemForm.examples.length === 0) {
      problemForm.examples = [{ input: "", output: "", explanation: "" }];
    }

    if (!problemForm.testCases || problemForm.testCases.length === 0) {
      problemForm.testCases = [{ input: "", output: "" }];
    }
  } else {
    // 创建新题目
    isEdit.value = false;
  }

  dialogVisible.value = true;
}

// 重置表单
function resetForm() {
  if (problemFormRef.value) {
    problemFormRef.value.resetFields();
  }

  Object.assign(problemForm, {
    id: null,
    title: "",
    difficulty: "",
    tags: [],
    description: "",
    inputDescription: "",
    outputDescription: "",
    examples: [{ input: "", output: "", explanation: "" }],
    testCases: [{ input: "", output: "" }],
    timeLimit: 1000,
    memoryLimit: 256,
  });
}

// 添加示例
function addExample() {
  problemForm.examples.push({ input: "", output: "", explanation: "" });
}

// 移除示例
function removeExample(index) {
  problemForm.examples.splice(index, 1);
}

// 添加测试用例
function addTestCase() {
  problemForm.testCases.push({ input: "", output: "" });
}

// 移除测试用例
function removeTestCase(index) {
  problemForm.testCases.splice(index, 1);
}

// 处理对话框关闭
function handleDialogClose(done) {
  if (submitting.value) {
    ElMessage.warning("正在提交，请稍候...");
    return;
  }

  ElMessageBox.confirm("确定要关闭吗？未保存的更改将会丢失", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      done();
    })
    .catch(() => {});
}

// 提交题目表单
async function submitProblemForm() {
  if (!problemFormRef.value) return;

  await problemFormRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.error("请完善表单信息");
      return;
    }

    submitting.value = true;

    try {
      const formData = { ...problemForm };

      // 过滤空的示例和测试用例
      formData.examples = formData.examples.filter(
        (ex) => ex.input.trim() || ex.output.trim()
      );
      formData.testCases = formData.testCases.filter(
        (tc) => tc.input.trim() || tc.output.trim()
      );

      if (formData.examples.length === 0) {
        ElMessage.error("至少需要一个示例");
        submitting.value = false;
        return;
      }

      if (formData.testCases.length === 0) {
        ElMessage.error("至少需要一个测试用例");
        submitting.value = false;
        return;
      }

      // 获取token
      let token = localStorage.getItem("token");
      console.log("当前token:", token ? "存在" : "不存在");

      // 临时解决方案：如果没有token，使用测试token
      if (!token) {
        console.log("没有找到token，使用测试token");
        token = "test_token";
        localStorage.setItem("token", token);
        ElMessage.warning("使用测试token，请确保已登录管理员账户");
      }

      // 准备请求配置
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      };

      let response;

      if (isEdit.value) {
        // 更新现有题目
        response = await axios.post(
          `http://127.0.0.1:5000/api/admin/questions?action=update_question`,
          formData,
          config
        );
        ElMessage.success(response.data.message || "题目更新成功");
      } else {
        // 创建新题目
        response = await axios.post(
          `http://127.0.0.1:5000/api/admin/questions?action=update_question`,
          formData,
          config
        );
        ElMessage.success(response.data.message || "题目创建成功");
      }

      dialogVisible.value = false;
      fetchProblems(); // 刷新题目列表
    } catch (error) {
      console.error("保存题目失败:", error);

      // 处理不同类型的错误
      if (error.response) {
        const errorMsg = error.response.data?.error || "保存题目失败";
        ElMessage.error(errorMsg);
      } else if (error.request) {
        ElMessage.error("网络错误，请检查连接");
      } else {
        ElMessage.error("保存题目失败，请稍后重试");
      }
    } finally {
      submitting.value = false;
    }
  });
}

// 查看题目
function viewProblem(problem) {
  router.push(`/problem/${problem.id}`);
}

// 删除题目
function deleteProblem(problem) {
  ElMessageBox.confirm(
    `确定要删除题目 "${problem.title}" 吗? 此操作不可恢复。`,
    "确认删除",
    { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning" }
  )
    .then(async () => {
      try {
        // 实际API调用
        let token = localStorage.getItem("token");
        console.log("删除题目时的token:", token ? "存在" : "不存在");

        // 临时解决方案：如果没有token，使用测试token
        if (!token) {
          console.log("没有找到token，使用测试token");
          token = "test_token";
          localStorage.setItem("token", token);
          ElMessage.warning("使用测试token，请确保已登录管理员账户");
        }

        const response = await axios.post(
          `http://127.0.0.1:5000/api/admin/questions?action=remove_question`,
          {
            id: problem.id,
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.status === 200) {
          ElMessage.success(
            response.data.message || `已删除题目: ${problem.title}`
          );
          fetchProblems(); // 刷新题目列表
        }
      } catch (error) {
        console.error("删除题目失败:", error);

        // 处理不同类型的错误
        if (error.response) {
          const errorMsg = error.response.data?.error || "删除题目失败";
          ElMessage.error(errorMsg);
        } else if (error.request) {
          ElMessage.error("网络错误，请检查连接");
        } else {
          ElMessage.error("删除题目失败，请稍后重试");
        }
      }
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
}

// 处理标签数据的辅助函数
function getTagsArray(tags) {
  if (!tags) return [];

  // 如果已经是数组，直接返回
  if (Array.isArray(tags)) {
    return tags;
  }

  // 如果是字符串，按逗号分割
  if (typeof tags === "string") {
    return tags
      .split(",")
      .map((tag) => tag.trim())
      .filter((tag) => tag.length > 0);
  }

  return [];
}

// 初始加载
onMounted(() => {
  fetchProblems();
});
</script>

<style scoped>
.problem-management {
  padding: 20px;
}

.filter-section {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.search-input {
  width: 300px;
}

.action-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.problem-table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.tag-item {
  margin-right: 5px;
  margin-bottom: 5px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.example-item,
.test-case-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: #f9f9f9;
}

.example-header,
.test-case-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-weight: bold;
}
</style>

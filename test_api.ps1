# 测试代码提交API
$headers = @{
    "Authorization" = "Bearer test_token"
    "Content-Type" = "application/json"
}

$body = @{
    user_id = 1
    question_id = 1
    code = "a, b = map(int, input().split())`nprint(a + b)"
    language = "python"
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/questions/1?action=submit_question" -Method POST -Headers $headers -Body $body
    Write-Host "状态码: $($response.StatusCode)"
    Write-Host "响应内容: $($response.Content)"
} catch {
    Write-Host "错误: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误响应: $responseBody"
    }
}

# 简单测试
Write-Host "开始测试API..."

# 测试获取题目信息
try {
    Write-Host "测试获取题目信息..."
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/questions/1?action=get_question" -Method GET -Headers @{'Authorization'='Bearer test_token'}
    Write-Host "状态码: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "获取题目失败: $($_.Exception.Message)"
}

Write-Host ""
Write-Host "=" * 50
Write-Host ""

# 测试提交代码
try {
    Write-Host "测试提交代码..."
    $body = '{"user_id": 1, "question_id": 1, "code": "print(\"Hello World\")"}'
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/questions/1?action=submit_question" -Method POST -Headers @{'Authorization'='Bearer test_token'; 'Content-Type'='application/json'} -Body $body
    Write-Host "状态码: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "提交失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

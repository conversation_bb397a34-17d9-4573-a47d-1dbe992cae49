USE OnlineJudge;
GO

-- 获取用户ID和题目ID
DECLARE @admin_id INT, @ques1_id INT, @ques2_id INT;

SELECT @admin_id = UserID FROM Users WHERE UserName = 'admin';
SELECT @ques1_id = QuesID FROM Questions WHERE QuesName = N'两数之和';
SELECT @ques2_id = QuesID FROM Questions WHERE QuesName = N'有效的括号';

-- 插入题解
INSERT INTO Answers (QuesID, AnsTitle, Content, AnsStatus, TIME, UserID)
VALUES
(@ques1_id, N'两数之和的最优解法', N'使用哈希表可以将时间复杂度降低到O(n)。遍历数组时，检查target-当前值是否在哈希表中，如果不在则将当前值加入哈希表。', N'approved', GETUTCDATE(), @admin_id),
(@ques2_id, N'栈解决括号匹配问题', N'使用栈结构可以轻松解决括号匹配问题。遇到左括号入栈，遇到右括号时检查栈顶是否匹配，不匹配则无效。', N'approved', GETUTCDATE(), @admin_id);
GO
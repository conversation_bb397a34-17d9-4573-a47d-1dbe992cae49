import sys
sys.path.append('backend')

from flask import Flask
from models import *
from database import db
import traceback

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = (
    'mssql+pyodbc://localhost/OnlineJudge?trusted_connection=yes&driver=ODBC+Driver+17+for+SQL+Server&trusted_connection=yes&TrustServerCertificate=yes')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

app.config['DB_CONNECTION_POOLS'] = {
    'default': {
        'host': 'localhost',
        'port': 1433,
        'user': 'admin',
        'password': 'admin',
        'database': 'OnlineJudge',
        'uri': 'mssql+pyodbc://localhost/OnlineJudge?trusted_connection=yes&driver=ODBC+Driver+17+for+SQL+Server&trusted_connection=yes&TrustServerCertificate=yes',
        'pool_size': 5,
        'max_overflow': 10
    }
}

db.init_app(app)

def test_questions_query():
    """测试题目查询功能"""
    with app.app_context():
        try:
            print("=== 测试数据库查询 ===")

            # 1. 基础查询测试
            print("1. 测试基础查询...")
            count = Questions.query.count()
            print(f"   题目总数: {count}")

            # 2. 测试分页查询
            print("2. 测试分页查询...")
            pg = 1
            ppg = 15

            query = Questions.query
            print(f"   基础查询对象: {query}")

            # 添加排序（MSSQL分页查询必须有ORDER BY）
            query = query.order_by(Questions.QuesID)
            print(f"   添加排序后的查询: {query}")

            paginated_questions = query.paginate(
                page=pg,
                per_page=ppg,
                error_out=False
            )
            print(f"   分页查询结果: {paginated_questions}")
            print(f"   获取到的题目数量: {len(paginated_questions.items)}")
            print(f"   总页数: {paginated_questions.pages}")
            print(f"   总记录数: {paginated_questions.total}")

            # 3. 测试具体题目数据
            print("3. 测试具体题目数据...")
            for i, question in enumerate(paginated_questions.items[:3]):
                print(f"   题目 {i+1}:")
                print(f"     ID: {question.QuesID}")
                print(f"     标题: {question.QuesName}")
                print(f"     难度: {question.QuesLevel}")
                print(f"     类型: {question.QuesType}")
                print(f"     通过数: {question.PassNum}")
                print(f"     提交数: {question.SubNum}")

            # 4. 模拟API返回格式
            print("4. 模拟API返回格式...")
            problems = []
            for question in paginated_questions.items:
                problems.append({
                    'id': question.QuesID,
                    'title': question.QuesName,
                    'difficulty': question.QuesLevel,
                    'tags': question.QuesType,
                    'accepted': question.PassNum,
                    'submitted': question.SubNum
                })

            result = {
                'problems': problems,
                'total': paginated_questions.total,
                'current_page': paginated_questions.page,
                'per_page': paginated_questions.per_page,
                'total_pages': paginated_questions.pages
            }

            print(f"   API返回格式: {result}")
            print("=== 测试完成，查询成功 ===")
            return True

        except Exception as e:
            print(f"=== 查询失败 ===")
            print(f"错误信息: {str(e)}")
            print("详细错误:")
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = test_questions_query()
    if success:
        print("\n✅ 数据库查询正常，问题可能在API路由或其他地方")
    else:
        print("\n❌ 数据库查询有问题，需要修复")

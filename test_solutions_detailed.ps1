# 详细测试题解API
Write-Host "详细测试题解API..."

# 测试题解API
Write-Host "1. 测试题解API..."
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/solutions?pg=1&ppg=10" -Method GET -Headers @{'Authorization'='Bearer test_token'}
    Write-Host "✅ 题解API测试: $($response.StatusCode)"
    
    $data = $response.Content | ConvertFrom-Json
    Write-Host "API返回的完整数据:"
    Write-Host ($data | ConvertTo-Json -Depth 5)
    
    Write-Host ""
    Write-Host "数据结构分析:"
    Write-Host "- solutions字段存在: $($data.solutions -ne $null)"
    Write-Host "- solutions类型: $($data.solutions.GetType().Name)"
    Write-Host "- solutions数量: $($data.solutions.Count)"
    Write-Host "- total字段: $($data.total)"
    
    if ($data.solutions.Count -gt 0) {
        Write-Host ""
        Write-Host "第一个题解的字段:"
        $firstSolution = $data.solutions[0]
        $firstSolution.PSObject.Properties | ForEach-Object {
            Write-Host "  $($_.Name): $($_.Value)"
        }
    }
    
} catch {
    Write-Host "❌ 题解API测试失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

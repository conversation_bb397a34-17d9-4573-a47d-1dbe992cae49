<template>
  <div class="solution-management">
    <h2>题解管理</h2>

    <!-- 搜索和筛选区域 -->
    <div class="filter-section">
      <el-input
        v-model="searchQuery"
        placeholder="搜索题解..."
        class="search-input"
        clearable
        @clear="fetchSolutions"
        @keyup.enter="searchSolutions"
      >
        <template #append>
          <el-button @click="searchSolutions">
            <el-icon><Search /></el-icon>
          </el-button>
        </template>
      </el-input>

      <el-select
        v-model="statusFilter"
        placeholder="状态"
        clearable
        @change="fetchSolutions"
      >
        <el-option label="已发布" value="published" />
        <el-option label="待审核" value="pending" />
        <el-option label="已拒绝" value="rejected" />
      </el-select>
    </div>

    <div class="action-bar">
      <el-button type="primary" @click="openSolutionDialog()">
        <el-icon><Plus /></el-icon> 新建题解
      </el-button>
      <el-button @click="fetchSolutions">
        <el-icon><Refresh /></el-icon> 刷新
      </el-button>
    </div>

    <div class="solution-table-container">
      <el-table :data="solutions" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" sortable />
        <el-table-column prop="AnsTitle" label="题解标题" width="250" />
        <el-table-column prop="ProblemTitle" label="关联题目" width="200" />
        <el-table-column prop="UserID" label="作者" width="120" />
        <el-table-column prop="Status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getSolutionStatusType(scope.row.Status)">
              {{ getSolutionStatusText(scope.row.Status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="TIME" label="创建时间" width="180" sortable>
          <template #default="scope">
            {{ formatDate(scope.row.TIME) }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button size="small" @click="openSolutionDialog(scope.row)"
              >编辑</el-button
            >
            <el-button
              size="small"
              type="primary"
              @click="viewSolution(scope.row)"
              >查看</el-button
            >
            <el-button
              size="small"
              :type="scope.row.Status === 'published' ? 'warning' : 'success'"
              @click="toggleSolutionStatus(scope.row)"
            >
              {{ scope.row.Status === "published" ? "下架" : "发布" }}
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="deleteSolution(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页控件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pg"
          v-model:page-size="ppg"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalSolutions"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 题解表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑题解' : '新建题解'"
      width="80%"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="solutionFormRef"
        :model="solutionForm"
        :rules="formRules"
        label-width="100px"
        label-position="top"
      >
        <el-form-item label="题解标题" prop="AnsTitle">
          <el-input
            v-model="solutionForm.AnsTitle"
            placeholder="请输入题解标题"
          />
        </el-form-item>

        <el-form-item label="关联题目" prop="ProblemID">
          <el-select
            v-model="solutionForm.ProblemID"
            filterable
            placeholder="请选择关联题目"
            class="full-width"
          >
            <el-option
              v-for="problem in problems"
              :key="problem.id"
              :label="problem.title"
              :value="problem.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="题解内容" prop="Content">
          <div class="editor-container">
            <el-tabs v-model="editorTab">
              <el-tab-pane label="编辑" name="edit">
                <el-input
                  v-model="solutionForm.Content"
                  type="textarea"
                  :rows="15"
                  placeholder="请输入题解内容，支持Markdown格式"
                />
              </el-tab-pane>
              <el-tab-pane label="预览" name="preview">
                <div class="markdown-preview" v-html="markdownPreview"></div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-form-item>

        <el-form-item label="标签" prop="Tags">
          <el-select
            v-model="solutionForm.Tags"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请选择或创建标签"
          >
            <el-option
              v-for="tag in solutionTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态" prop="Status">
          <el-radio-group v-model="solutionForm.Status">
            <el-radio label="draft">草稿</el-radio>
            <el-radio label="pending">待审核</el-radio>
            <el-radio label="published">发布</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitSolutionForm"
            :loading="submitting"
          >
            {{ isEdit ? "保存修改" : "创建题解" }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Search, Refresh } from "@element-plus/icons-vue";
import axios from "axios";
import { marked } from "marked";
import DOMPurify from "dompurify";

const router = useRouter();
const solutions = ref([]);
const problems = ref([]);
const loading = ref(true);
const submitting = ref(false);
const dialogVisible = ref(false);
const isEdit = ref(false);
const solutionFormRef = ref(null);
const searchQuery = ref("");
const statusFilter = ref("");
const pg = ref(1);
const ppg = ref(10);
const totalSolutions = ref(0);
const editorTab = ref("edit");

// 题解标签
const solutionTags = ref([
  "算法思路",
  "代码实现",
  "时间复杂度",
  "空间复杂度",
  "优化方法",
  "动态规划",
  "贪心算法",
  "分治法",
  "回溯法",
  "数学证明",
]);

// 表单数据
const solutionForm = reactive({
  id: null,
  AnsTitle: "",
  ProblemID: null,
  Content: "",
  Status: "draft",
  UserID: "",
});

// 表单验证规则
const formRules = {
  AnsTitle: [
    { required: true, message: "请输入题解标题", trigger: "blur" },
    {
      min: 3,
      max: 100,
      message: "标题长度应在3到100个字符之间",
      trigger: "blur",
    },
  ],
  ProblemID: [{ required: true, message: "请选择关联题目", trigger: "change" }],
  Content: [
    { required: true, message: "请输入题解内容", trigger: "blur" },
    { min: 10, message: "内容太短，请详细描述您的解题思路", trigger: "blur" },
  ],
};

// Markdown预览
const markdownPreview = computed(() => {
  if (!solutionForm.Content) return "";
  const rawHtml = marked(solutionForm.Content);
  return DOMPurify.sanitize(rawHtml);
});

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return "未知时间";

  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
}

// 获取题解列表
async function fetchSolutions() {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {
      pg: pg.value,
      ppg: ppg.value,
    };

    if (searchQuery.value) {
      params.search = searchQuery.value;
    }

    if (statusFilter.value) {
      params.status = statusFilter.value;
    }

    // 获取token
    let token = localStorage.getItem("token");
    console.log("当前token:", token ? "存在" : "不存在");

    // 临时解决方案：如果没有token，使用测试token
    if (!token) {
      console.log("没有找到token，使用测试token");
      token = "test_token";
      localStorage.setItem("token", token);
      ElMessage.warning("使用测试token，请确保已登录管理员账户");
    }

    // 实际API调用
    const response = await axios.get("http://127.0.0.1:5000/api/solutions", {
      params,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (response.status === 200) {
      solutions.value = response.data.solutions || [];
      totalSolutions.value = response.data.total || 0;
    } else {
      throw new Error("获取题解列表失败");
    }
  } catch (error) {
    console.error("获取题解列表失败:", error);

    // 详细的错误处理
    if (error.response) {
      const status = error.response.status;
      const errorMsg =
        error.response.data?.error ||
        error.response.data?.message ||
        "获取题解列表失败";

      if (status === 401) {
        ElMessage.error("登录已过期，请重新登录");
        // 可以在这里跳转到登录页面
        // router.push('/login');
      } else if (status === 403) {
        ElMessage.error("权限不足，需要管理员权限");
      } else {
        ElMessage.error(`获取题解列表失败: ${errorMsg}`);
      }
    } else if (error.request) {
      ElMessage.error("网络错误，请检查服务器连接");
    } else {
      ElMessage.error("获取题解列表失败，请稍后重试");
    }

    // 如果API调用失败，使用模拟数据（仅用于开发）
    solutions.value = [
      {
        id: 1,
        AnsTitle: "两数之和的最优解法",
        ProblemTitle: "两数之和",
        UserID: "admin",
        Status: "published",
        TIME: "2023-05-15 14:30:22",
      },
      {
        id: 2,
        AnsTitle: "链表反转详解",
        ProblemTitle: "链表反转",
        UserID: "teacher1",
        Status: "published",
        TIME: "2023-06-20 09:15:43",
      },
      {
        id: 3,
        AnsTitle: "动态规划解决回文子串",
        ProblemTitle: "最长回文子串",
        UserID: "admin",
        Status: "pending",
        TIME: "2023-07-05 16:45:10",
      },
    ];
    totalSolutions.value = solutions.value.length;
  } finally {
    loading.value = false;
  }
}

// 获取题目列表（用于关联题目选择）
async function fetchProblems() {
  try {
    const response = await axios.get(
      "http://127.0.0.1:5000/api/problems?action=get_solutions",
      {
        params: { per_page: 100 }, // 获取足够多的题目供选择
      }
    );

    if (response.status === 200) {
      problems.value = response.data.problems;
    } else {
      throw new Error("获取题目列表失败");
    }
  } catch (error) {
    console.error("获取题目列表失败:", error);

    // 详细的错误处理
    if (error.response) {
      const status = error.response.status;
      const errorMsg = error.response.data?.error || error.response.data?.message || "获取题目列表失败";

      if (status === 401) {
        ElMessage.warning("登录已过期，题目关联功能可能受限");
      } else if (status === 403) {
        ElMessage.warning("权限不足，题目关联功能可能受限");
      } else {
        ElMessage.warning(`获取题目列表失败: ${errorMsg}，可能无法关联题目`);
      }
    } else if (error.request) {
      ElMessage.warning("网络错误，题目关联功能可能受限");
    } else {
      ElMessage.warning("获取题目列表失败，可能无法关联题目");
    }

    // 如果API调用失败，使用模拟数据
    problems.value = [
      { id: 1, title: "两数之和" },
      { id: 2, title: "链表反转" },
      { id: 3, title: "最长回文子串" },
    ];
  }
}

// 搜索题解
function searchSolutions() {
  pg.value = 1; // 重置到第一页
  fetchSolutions();
}

// 处理分页大小变化
function handleSizeChange(size) {
  ppg.value = size;
  fetchSolutions();
}

// 处理页码变化
function handleCurrentChange(page) {
  pg.value = page;
  fetchSolutions();
}

// 获取题解状态对应的标签类型
function getSolutionStatusType(status) {
  switch (status) {
    case "published":
      return "success";
    case "pending":
      return "warning";
    case "rejected":
      return "danger";
    case "draft":
      return "info";
    default:
      return "info";
  }
}

// 获取题解状态的显示文本
function getSolutionStatusText(status) {
  switch (status) {
    case "published":
      return "已发布";
    case "pending":
      return "待审核";
    case "rejected":
      return "已拒绝";
    case "draft":
      return "草稿";
    default:
      return "未知";
  }
}

// 打开题解表单对话框
function openSolutionDialog(solution = null) {
  resetForm();

  if (solution) {
    // 编辑现有题解
    isEdit.value = true;
    Object.keys(solutionForm).forEach((key) => {
      if (key in solution) {
        solutionForm[key] = solution[key];
      }
    });

    // 设置关联题目ID
    if (solution.ProblemID) {
      solutionForm.ProblemID = solution.ProblemID;
    }
  } else {
    // 创建新题解
    isEdit.value = false;
  }

  dialogVisible.value = true;
}

// 重置表单
function resetForm() {
  if (solutionFormRef.value) {
    solutionFormRef.value.resetFields();
  }

  Object.assign(solutionForm, {
    id: null,
    AnsTitle: "",
    ProblemID: null,
    Content: "",
    Tags: [],
    Status: "",
    UserID: "",
  });

  editorTab.value = "edit";
}

// 处理对话框关闭
function handleDialogClose(done) {
  if (submitting.value) {
    ElMessage.warning("正在提交，请稍候...");
    return;
  }

  ElMessageBox.confirm("确定要关闭吗？未保存的更改将会丢失", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      done();
    })
    .catch(() => {});
}

// 提交题解表单
async function submitSolutionForm() {
  if (!solutionFormRef.value) return;

  await solutionFormRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.error("请完善表单信息");
      return;
    }

    submitting.value = true;

    try {
      // 获取token
      const token = localStorage.getItem("token");
      if (!token) {
        ElMessage.error("您的登录已过期，请重新登录");
        submitting.value = false;
        return;
      }

      const formData = { ...solutionForm };
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      };

      let response;

      if (isEdit.value) {
        // 更新现有题解
        response = await axios.post(
          `http://127.0.0.1:5000/api/solutions?action=update_solutions`,
          formData,
          config
        );
        ElMessage.success("题解更新成功");
      } else {
        // 创建新题解
        response = await axios.post(
          "http://127.0.0.1:5000/api/solutions?action=update_solutions",
          formData,
          config
        );
        ElMessage.success("题解创建成功");
      }

      dialogVisible.value = false;
      fetchSolutions(); // 刷新题解列表
    } catch (error) {
      console.error("保存题解失败:", error);
      ElMessage.error("保存题解失败，请稍后重试");
    } finally {
      submitting.value = false;
    }
  });
}

// 查看题解
function viewSolution(solution) {
  router.push(`/solution/${solution.id}`);
}

// 切换题解状态（发布/下架）
async function toggleSolutionStatus(solution) {
  try {
    const newStatus = solution.Status === "published" ? "draft" : "published";
    const action = solution.Status === "published" ? "下架" : "发布";

    await axios.post(
      `http://127.0.0.1:5000/api/solutions?action=update_solution_status`,
      {
        id: solution.id,
        status: newStatus,
      }
    );

    ElMessage.success(`题解已${action}`);
    fetchSolutions(); // 刷新列表
  } catch (error) {
    console.error("更改题解状态失败:", error);
    ElMessage.error("操作失败，请稍后重试");
  }
}

// 删除题解
function deleteSolution(solution) {
  ElMessageBox.confirm(
    `确定要删除题解 "${solution.AnsTitle}" 吗? 此操作不可恢复。`,
    "确认删除",
    { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning" }
  )
    .then(async () => {
      try {
        // 获取token
        const token = localStorage.getItem("token");
        if (!token) {
          ElMessage.error("您的登录已过期，请重新登录");
          return;
        }

        // 实际API调用
        await axios.post(
          `http://127.0.0.1:5000/api/solutions?action=remove_solutions`,
          {
            ans_id: solution.id,
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );
        ElMessage.success(`已删除题解: ${solution.AnsTitle}`);
        fetchSolutions(); // 刷新题解列表
      } catch (error) {
        console.error("删除题解失败:", error);

        // 处理不同类型的错误
        if (error.response) {
          const errorMsg = error.response.data?.error || "删除题解失败";
          ElMessage.error(errorMsg);
        } else if (error.request) {
          ElMessage.error("网络错误，请检查连接");
        } else {
          ElMessage.error("删除题解失败，请稍后重试");
        }
      }
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
}

// 初始加载
onMounted(() => {
  fetchSolutions();
  fetchProblems();
});
</script>

<style scoped>
.solution-management {
  padding: 20px;
}

.filter-section {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.search-input {
  width: 300px;
}

.action-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.solution-table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.markdown-preview {
  min-height: 300px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f9f9f9;
  overflow-y: auto;
}

.full-width {
  width: 100%;
}
</style>

<template>
  <div class="back-home">
    <button @click="goHome">← 返回主页</button>
  </div>

  <!-- 添加题解按钮 -->
  <div class="view-solution">
    <button @click="viewSolution">查看题解</button>
  </div>

  <div v-if="loading" class="loading">
    <div class="spinner"></div>
    <p>加载中...</p>
  </div>

  <div v-else-if="error" class="error-message">
    {{ error }}
  </div>

  <div v-else class="problem-page">
    <!-- 左侧题目详情 -->
    <section class="problem-detail">
      <h1>{{ selectedProblem.title }}</h1>
      <div class="problem-actions">
        <button
          @click="toggleFavorite"
          class="favorite-btn"
          :class="{ 'is-favorite': isFavorite }"
        >
          <span v-if="isFavorite">★ 已收藏</span>
          <span v-else>☆ 收藏</span>
        </button>
      </div>
      <div class="problem-meta">
        <span class="difficulty" :class="selectedProblem.difficulty">
          {{ selectedProblem.difficulty }}
        </span>
        <span
          v-for="tag in getTagsArray(selectedProblem.tags)"
          :key="tag"
          class="tag"
        >
          {{ tag }}
        </span>
      </div>

      <div class="problem-content">
        <h3>题目描述</h3>
        <div v-html="selectedProblem.description"></div>

        <h3>输入格式</h3>
        <p>{{ selectedProblem.inputFormat }}</p>

        <h3>输出格式</h3>
        <p>{{ selectedProblem.outputFormat }}</p>

        <h3>示例</h3>
        <div class="example">
          <div>
            <h5>输入:</h5>
            <pre>{{ selectedProblem.inputExample }}</pre>
          </div>
          <div>
            <h5>输出:</h5>
            <pre>{{ selectedProblem.outputExample }}</pre>
          </div>
        </div>

        <!-- 评论区 -->
        <div class="comments-section">
          <button @click="toggleComments" class="toggle-btn">
            {{ showComments ? "隐藏评论" : "查看评论" }}
          </button>

          <div v-if="showComments" class="comments-area">
            <div v-if="comments.length === 0" class="no-comments">
              暂无评论，快来发表第一条评论吧！
            </div>

            <div
              v-for="(comment, index) in comments"
              :key="comment.id"
              class="comment"
            >
              <div class="comment-header">
                <strong>{{ comment.user }}</strong>
                <span class="comment-time">{{ comment.time }}</span>
              </div>
              <p>{{ comment.text }}</p>
              <button @click="toggleReply(index)" class="reply-btn">
                {{ activeReplyIndex === index ? "取消回复" : "回复" }}
              </button>

              <div v-if="activeReplyIndex === index" class="reply-box">
                <input
                  v-model="replyText"
                  placeholder="回复..."
                  @keyup.enter="submitReply(comment.id, index)"
                />
                <button @click="submitReply(comment.id, index)">发送</button>
              </div>

              <div
                v-if="comment.replies && comment.replies.length > 0"
                class="replies"
              >
                <div
                  v-for="reply in comment.replies"
                  :key="reply.id"
                  class="reply"
                >
                  <strong>{{ reply.user }}</strong
                  >: {{ reply.text }}
                </div>
              </div>
            </div>

            <!-- 新评论 -->
            <div class="new-comment">
              <input
                v-model="newComment"
                placeholder="发表评论..."
                @keyup.enter="submitComment"
              />
              <button @click="submitComment">发送</button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 右侧代码编辑 -->
    <section class="code-editor">
      <h3>代码编辑器</h3>
      <textarea
        v-model="code"
        rows="25"
        placeholder="在这里编写代码..."
      ></textarea>
      <button @click="submitCode">提交代码</button>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import axios from "axios";

const router = useRouter();
const route = useRoute();

function goHome() {
  router.push("/home");
}

// 添加查看题解的函数
function viewSolution() {
  router.push(`/solution/${route.params.id}`);
}

// 初始化一个空的问题对象
const selectedProblem = ref({
  id: 0,
  title: "",
  difficulty: "",
  tags: [],
  description: "",
  inputFormat: "",
  outputFormat: "",
  inputExample: "",
  outputExample: "",
});

// 加载状态
const loading = ref(true);
const error = ref("");

// 评论相关状态
const showComments = ref(false);
const comments = ref([]);
const newComment = ref("");
const replyText = ref("");
const activeReplyIndex = ref(null);
const code = ref("");
const isSubmitting = ref(false); // 添加提交状态变量

// 添加收藏相关状态
const isFavorite = ref(false);
const favoriteLoading = ref(false);

// 根据路由参数加载题目
async function fetchProblem(id) {
  loading.value = true;
  error.value = "";

  try {
    console.log("正在获取题目详情，ID:", id);

    // 获取token
    const token = localStorage.getItem("token");
    console.log("Token存在:", !!token);

    if (!token) {
      console.log("没有token，重定向到登录页");
      router.push("/login");
      return;
    }

    const headers = {
      Authorization: `Bearer ${token}`,
    };

    console.log(
      "发送请求到:",
      `http://127.0.0.1:5000/api/questions/${id}?action=get_question`
    );

    const response = await axios.get(
      `http://127.0.0.1:5000/api/questions/${id}?action=get_question`,
      {
        headers,
      }
    );

    console.log("API响应:", response);

    if (response.status === 200) {
      const problemData = response.data;
      console.log("题目数据:", problemData);

      // 处理 examples 数组到 inputExample 和 outputExample
      if (problemData.examples && problemData.examples.length > 0) {
        problemData.inputExample = problemData.examples[0].input || "";
        problemData.outputExample = problemData.examples[0].output || "";
      } else {
        problemData.inputExample = problemData.inputExample || "";
        problemData.outputExample = problemData.outputExample || "";
      }

      selectedProblem.value = problemData;
    } else {
      throw new Error("获取题目失败");
    }
  } catch (err) {
    console.error("获取题目失败:", err);
    if (err.response && err.response.status === 401) {
      console.log("认证失败，重定向到登录页");
      router.push("/login");
    } else {
      error.value = `获取题目失败，请稍后重试: ${err.message}`;
    }
  } finally {
    loading.value = false;
  }
}

// 获取题目评论
async function fetchComments(problemId) {
  try {
    // 修改为与后端 reviews.py 匹配的 API 路径
    const response = await axios.get(
      `http://127.0.0.1:5000/api/questions/${problemId}?action=get_review`
    );

    if (response.status === 200) {
      comments.value = response.data;
    }
  } catch (err) {
    console.error("获取评论失败:", err);
  }
}

// 提交评论
async function submitComment() {
  const text = newComment.value.trim();
  if (!text) return;

  const token = localStorage.getItem("token");
  if (!token) {
    router.push("/login");
    return;
  }

  isSubmitting.value = true; // 设置提交状态

  try {
    const response = await axios.post(
      `http://127.0.0.1:5000/api/questions/${selectedProblem.value.id}?action=submit_review`,
      {
        user_id: getUserIdFromToken(token), // 需要从 token 中获取用户 ID
        question_id: selectedProblem.value.id,
        parent_review_id: null, // 顶级评论没有父评论
        content: text,
      },
      { headers: { Authorization: `Bearer ${token}` } }
    );

    if (response.status === 201) {
      // 添加新评论到列表
      comments.value.push({
        id: response.data.review_id || Date.now(), // 使用后端返回的ID或临时ID
        user: getCurrentUsername(), // 获取当前用户名
        text: text,
        time: new Date().toLocaleString(), // 添加时间
        replies: [],
      });
      newComment.value = "";
    }
  } catch (err) {
    console.error("提交评论失败:", err);
    alert("提交评论失败，请稍后重试");
  } finally {
    isSubmitting.value = false; // 重置提交状态
  }
}

// 提交回复
async function submitReply(commentId, index) {
  const text = replyText.value.trim();
  if (!text) return;

  const token = localStorage.getItem("token");
  if (!token) {
    router.push("/login");
    return;
  }

  try {
    const response = await axios.post(
      `http://127.0.0.1:5000/api/questions/${selectedProblem.value.id}?action=submit_review`,
      {
        user_id: getUserIdFromToken(token), // 需要从 token 中获取用户 ID
        question_id: selectedProblem.value.id,
        parent_review_id: commentId,
        content: text,
      },
      { headers: { Authorization: `Bearer ${token}` } }
    );

    if (response.status === 201) {
      // 添加新回复到评论
      if (!comments.value[index].replies) {
        comments.value[index].replies = [];
      }
      comments.value[index].replies.push({
        user: getCurrentUsername(), // 获取当前用户名
        text: text,
      });
      replyText.value = "";
      activeReplyIndex.value = null;
    }
  } catch (err) {
    console.error("提交回复失败:", err);
    alert("提交回复失败，请稍后重试");
  }
}

// 提交代码
async function submitCode() {
  if (!code.value.trim()) {
    alert("请输入代码");
    return;
  }

  const token = localStorage.getItem("token");
  if (!token) {
    router.push("/login");
    return;
  }

  try {
    const response = await axios.post(
      `http://127.0.0.1:5000/api/questions/${selectedProblem.value.id}?action=submit_question`,
      {
        user_id: getUserIdFromToken(token), // 需要从 token 中获取用户 ID
        question_id: selectedProblem.value.id,
        code: code.value,
      },
      { headers: { Authorization: `Bearer ${token}` } }
    );

    if (response.status === 200) {
      // 显示提交结果
      alert(
        `提交结果: ${response.data.result}\n执行时间: ${response.data.executionTime}ms\n内存使用: ${response.data.memoryUsage}KB`
      );
    }
  } catch (err) {
    console.error("提交代码失败:", err);
    alert("提交代码失败，请稍后重试");
  }
}

function toggleComments() {
  showComments.value = !showComments.value;

  // 如果打开评论区且还没有加载评论，则加载评论
  if (showComments.value && comments.value.length === 0) {
    fetchComments(selectedProblem.value.id);
  }
}

function toggleReply(index) {
  activeReplyIndex.value = activeReplyIndex.value === index ? null : index;
  replyText.value = "";
}

// 检查题目是否已收藏
async function checkIfFavorite() {
  const token = localStorage.getItem("token");
  if (!token) return;

  try {
    const response = await axios.post(
      `http://127.0.0.1:5000/api/questions/${route.params.id}?action=check_favorite`,
      {
        problem_id: parseInt(route.params.id),
        user_id: getUserIdFromToken(token),
      },
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );

    if (response.status === 200) {
      // 直接使用后端返回的收藏状态
      isFavorite.value = response.data.is_favorite;
    }
  } catch (err) {
    console.error("检查收藏状态失败:", err);
  }
}

// 切换收藏状态
async function toggleFavorite() {
  const token = localStorage.getItem("token");
  if (!token) {
    router.push("/login");
    return;
  }

  favoriteLoading.value = true;

  try {
    if (isFavorite.value) {
      // 取消收藏 - 改用POST方法
      const response = await axios.post(
        `http://127.0.0.1:5000/api/questions/${selectedProblem.value.id}?action=remove_favorite`,
        {
          problem_id: selectedProblem.value.id,
          user_id: getUserIdFromToken(token),
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (response.status === 200) {
        isFavorite.value = false;
      }
    } else {
      // 添加收藏
      const response = await axios.post(
        `http://127.0.0.1:5000/api/questions/${selectedProblem.value.id}?action=add_favorite`,
        {
          problem_id: selectedProblem.value.id,
          user_id: getUserIdFromToken(token),
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (response.status === 201) {
        isFavorite.value = true;
      }
    }
  } catch (err) {
    console.error("更新收藏状态失败:", err);
    alert("操作失败，请稍后重试");
  } finally {
    favoriteLoading.value = false;
  }
}

// 页面加载时获取题目数据和检查收藏状态
onMounted(() => {
  const problemId = route.params.id;

  if (problemId) {
    fetchProblem(problemId);
    checkIfFavorite();
  } else {
    // 如果没有ID参数，重定向到题目列表页
    router.push("/home");
  }
});

// 从 token 中获取用户 ID 的辅助函数
function getUserIdFromToken(token) {
  // 实际应用中，您需要解析 JWT token 来获取用户 ID
  // 这里简化处理，假设本地存储中有用户信息
  const userInfo = JSON.parse(localStorage.getItem("userInfo") || "{}");
  return userInfo.id || 1; // 默认返回 1，实际应用中应该返回真实的用户 ID
}

// 获取当前用户名的辅助函数
function getCurrentUsername() {
  const userInfo = JSON.parse(localStorage.getItem("userInfo") || "{}");
  return userInfo.username || "当前用户"; // 默认返回"当前用户"
}

// 处理标签数据的辅助函数
function getTagsArray(tags) {
  if (!tags) return [];

  // 如果已经是数组，直接返回
  if (Array.isArray(tags)) {
    return tags;
  }

  // 如果是字符串，按逗号分割
  if (typeof tags === "string") {
    return tags
      .split(",")
      .map((tag) => tag.trim())
      .filter((tag) => tag.length > 0);
  }

  return [];
}
</script>

<style scoped>
.back-home {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.back-home button {
  background-color: #409eff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.back-home button:hover {
  background-color: #66b1ff;
}

/* 添加题解按钮样式 */
.view-solution {
  position: fixed;
  top: 20px;
  right: 120px; /* 放在返回主页按钮的左侧 */
  z-index: 1000;
}

.view-solution button {
  background-color: #67c23a;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.view-solution button:hover {
  background-color: #85ce61;
}

/* 添加加载和错误状态样式 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #409eff;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: #f56c6c;
  font-size: 18px;
  text-align: center;
  padding: 0 20px;
}

.problem-page {
  display: flex;
  height: 100vh;
  padding: 20px;
  gap: 20px;
  font-family: Arial, sans-serif;
  box-sizing: border-box;
}

.problem-detail {
  flex: 1.5;
  overflow-y: auto;
  border: 1px solid #ddd;
  padding: 20px;
  border-radius: 6px;
  background-color: #fafafa;
}

.problem-meta {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.difficulty {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.difficulty.简单 {
  background-color: #67c23a;
  color: white;
}

.difficulty.中等 {
  background-color: #e6a23c;
  color: white;
}

.difficulty.困难 {
  background-color: #f56c6c;
  color: white;
}

.tag {
  background-color: #ecf5ff;
  color: #409eff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.example pre {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
}

.code-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #ddd;
  padding: 20px;
  border-radius: 6px;
  background-color: #fff;
}

.code-editor textarea {
  flex-grow: 1;
  font-family: monospace;
  font-size: 14px;
  padding: 12px;
  resize: vertical;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
}

.code-editor button {
  margin-top: 12px;
  padding: 10px;
  background-color: #409eff;
  border: none;
  color: white;
  cursor: pointer;
  border-radius: 4px;
}
.code-editor button:hover {
  background-color: #66b1ff;
}

.comments-section {
  margin-top: 30px;
}

.toggle-btn {
  background-color: #409eff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
}
.toggle-btn:hover {
  background-color: #66b1ff;
}

.comments-area {
  margin-top: 15px;
  background: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
}

.no-comments {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}

.comment {
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid #ddd;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.comment-time {
  font-size: 12px;
  color: #909399;
}

.reply-btn {
  margin-left: 10px;
  background: none;
  border: none;
  color: #409eff;
  cursor: pointer;
}

.reply-btn:hover {
  text-decoration: underline;
}

.reply-box {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.reply-box input {
  flex: 1;
  padding: 6px;
  border-radius: 4px;
  border: 1px solid #ccc;
}

.reply-box button {
  padding: 6px 12px;
  background-color: #67c23a;
  border: none;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

.reply-box button:hover {
  background-color: #85ce61;
}

.replies {
  margin-left: 20px;
  margin-top: 6px;
}

.reply {
  padding: 4px 0;
}

.new-comment {
  display: flex;
  gap: 8px;
  margin-top: 10px;
}

.new-comment input {
  flex: 1;
  padding: 6px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.new-comment button {
  background-color: #67c23a;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
}

.new-comment button:hover {
  background-color: #85ce61;
}

/* 收藏按钮样式 */
.problem-actions {
  margin: 10px 0 20px;
}

.favorite-btn {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.favorite-btn:hover {
  background-color: #e0e0e0;
}

.favorite-btn.is-favorite {
  background-color: #fff8e1;
  border-color: #ffd54f;
  color: #ff9800;
}

.favorite-btn.is-favorite:hover {
  background-color: #ffecb3;
}
</style>

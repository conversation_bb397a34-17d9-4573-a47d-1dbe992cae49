import requests
import json

# 测试删除题目API
def test_delete_question():
    url = "http://127.0.0.1:5000/api/admin/questions?action=remove_question"
    headers = {
        'Authorization': 'Bearer test_token',
        'Content-Type': 'application/json'
    }
    
    # 测试删除不存在的题目
    print("测试删除不存在的题目...")
    data = {"id": 999999}
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_delete_question()

<template>
  <div class="user-profile">
    <div class="header-actions">
      <button class="home-btn" @click="goHome">返回首页</button>
      <button class="logout-btn" @click="logout">退出登录</button>
    </div>

    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>加载中...</p>
    </div>

    <div v-if="errorMsg" class="error-container">
      <p>{{ errorMsg }}</p>
      <button @click="fetchUserProfile">重试</button>
    </div>

    <div v-if="!loading && !errorMsg" class="profile-container">
      <!-- 用户信息卡片 -->
      <div class="profile-card">
        <div class="avatar-container">
          <img :src="user.avatar" alt="头像" class="avatar" />
          <button class="change-avatar-btn" @click="openAvatarModal">
            更换头像
          </button>
        </div>
        <div class="username-container">
          <h2>{{ user.username }}</h2>
          <button class="edit-btn" @click="openUsernameModal">编辑</button>
        </div>
        <p class="join-date">注册时间：{{ user.joinDate }}</p>

        <!-- 用户统计数据 -->
        <div class="stats">
          <div class="stat-item">
            <h3>{{ user.solved }}</h3>
            <p>已解决</p>
          </div>
          <div class="stat-item">
            <h3>{{ user.submitted }}</h3>
            <p>提交总数</p>
          </div>
          <div class="stat-item">
            <h3>{{ user.accuracy }}%</h3>
            <p>正确率</p>
          </div>
        </div>
      </div>

      <!-- 在收藏夹部分下方添加提交记录部分 -->
      <div class="submissions-section">
        <h2>我的提交记录</h2>
        <div v-if="loadingSubmissions" class="loading-spinner">
          <div class="spinner"></div>
        </div>
        <div v-else-if="submissions.length === 0" class="empty-submissions">
          您还没有提交过任何题目
        </div>
        <div v-else class="submissions-list">
          <table class="submissions-table">
            <thead>
              <tr>
                <th>题目</th>
                <th>提交时间</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="submission in submissions"
                :key="submission.id"
                class="submission-item"
              >
                <td>{{ submission.problem_title }}</td>
                <td>{{ formatDate(submission.submit_time) }}</td>
                <td>
                  <span
                    :class="['status-badge', getStatusClass(submission.status)]"
                  >
                    {{ getStatusText(submission.status) }}
                  </span>
                </td>
                <td>
                  <button
                    class="view-btn"
                    @click="viewProblem(submission.problem_id)"
                  >
                    查看题目
                  </button>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- 分页控件 -->
          <div class="pagination">
            <button
              :disabled="currentPage === 1"
              @click="changePage(currentPage - 1)"
              class="page-btn"
            >
              上一页
            </button>
            <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
            <button
              :disabled="currentPage === totalPages"
              @click="changePage(currentPage + 1)"
              class="page-btn"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 修改用户名弹窗 -->
    <transition name="fade">
      <div
        v-if="showUsernameModal"
        class="modal"
        @click.self="cancelUsernameChange"
      >
        <div class="modal-content">
          <h3>修改用户名</h3>
          <div class="form-group">
            <label for="username">新用户名</label>
            <input
              id="username"
              v-model="newUsername"
              placeholder="请输入新用户名"
              :class="{ 'input-error': usernameError }"
              @input="validateUsername"
            />
            <p v-if="usernameError" class="error-text">{{ usernameError }}</p>
          </div>
          <div class="modal-actions">
            <button @click="cancelUsernameChange" class="cancel-btn">
              取消
            </button>
            <button
              @click="saveUsername"
              class="save-btn"
              :disabled="!isUsernameValid || updatingUsername"
            >
              <span v-if="updatingUsername" class="btn-spinner"></span>
              <span v-else>保存</span>
            </button>
          </div>
        </div>
      </div>
    </transition>

    <!-- 修改头像弹窗 -->
    <transition name="fade">
      <div
        v-if="showAvatarModal"
        class="modal"
        @click.self="cancelAvatarChange"
      >
        <div class="modal-content">
          <h3>修改头像</h3>
          <div class="form-group">
            <label for="avatar-url">头像URL</label>
            <input
              id="avatar-url"
              v-model="newAvatarUrl"
              placeholder="请输入头像URL"
              :class="{ 'input-error': avatarError }"
              @input="validateAvatarUrl"
            />
            <p v-if="avatarError" class="error-text">{{ avatarError }}</p>
          </div>
          <div class="avatar-preview">
            <img :src="previewAvatarUrl" alt="预览" />
          </div>
          <div class="modal-actions">
            <button @click="cancelAvatarChange" class="cancel-btn">取消</button>
            <button
              @click="saveAvatar"
              class="save-btn"
              :disabled="!isAvatarValid || updatingAvatar"
            >
              <span v-if="updatingAvatar" class="btn-spinner"></span>
              <span v-else>保存</span>
            </button>
          </div>
        </div>
      </div>
    </transition>

    <!-- 在用户信息卡片下方添加收藏夹部分 -->
    <div class="favorites-section">
      <h2>我的收藏夹</h2>
      <div v-if="loadingFavorites" class="loading-spinner">
        <div class="spinner"></div>
      </div>
      <div v-else-if="favorites.length === 0" class="empty-favorites">
        您还没有收藏任何题目
      </div>
      <div v-else class="favorites-list">
        <div
          v-for="favorite in favorites"
          :key="favorite.problem_id"
          class="favorite-item"
        >
          <div class="favorite-info">
            <h3>{{ favorite.title }}</h3>
            <p class="favorite-date">收藏于: {{ favorite.added_at }}</p>
          </div>
          <div class="favorite-actions">
            <button class="view-btn" @click="viewProblem(favorite.problem_id)">
              查看题目
            </button>
            <button
              class="remove-btn"
              @click="removeFavorite(favorite.problem_id)"
            >
              移除
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import DOMPurify from "dompurify";

const router = useRouter();

// 用户数据
const user = ref({
  username: "",
  email: "",
  joinDate: "",
  avatar: "",
  solved: 0,
  submitted: 0,
  accuracy: 0,
});

// 加载和错误状态
const loading = ref(true);
const errorMsg = ref("");

// 用户名修改相关
const showUsernameModal = ref(false);
const newUsername = ref("");
const usernameError = ref("");
const updatingUsername = ref(false);

// 头像修改相关
const showAvatarModal = ref(false);
const newAvatarUrl = ref("");
const avatarError = ref("");
const updatingAvatar = ref(false);

// 验证计算属性
const isUsernameValid = computed(
  () => !usernameError.value && newUsername.value.trim() !== ""
);
const isAvatarValid = computed(
  () => !avatarError.value && newAvatarUrl.value.trim() !== ""
);

// 头像预览URL
const previewAvatarUrl = computed(() => {
  if (!newAvatarUrl.value.trim() || !isValidUrl(newAvatarUrl.value)) {
    return user.value.avatar;
  }
  return newAvatarUrl.value.trim();
});

// 验证URL是否有效
function isValidUrl(string) {
  if (!string) return false;
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

// 验证用户名
function validateUsername() {
  const username = newUsername.value.trim();

  if (!username) {
    usernameError.value = "用户名不能为空";
    return;
  }

  if (username.length < 3) {
    usernameError.value = "用户名至少需要3个字符";
    return;
  }

  if (username.length > 20) {
    usernameError.value = "用户名不能超过20个字符";
    return;
  }

  usernameError.value = "";
}

// 验证头像URL
function validateAvatarUrl() {
  const url = newAvatarUrl.value.trim();

  if (!url) {
    avatarError.value = "请输入头像URL";
    return;
  }

  if (!isValidUrl(url)) {
    avatarError.value = "请输入有效的URL";
    return;
  }

  avatarError.value = "";
}

// 打开用户名修改弹窗
function openUsernameModal() {
  newUsername.value = user.value.username;
  usernameError.value = "";
  showUsernameModal.value = true;
}

// 打开头像修改弹窗
function openAvatarModal() {
  newAvatarUrl.value = user.value.avatar;
  avatarError.value = "";
  showAvatarModal.value = true;
}

// 获取用户信息
async function fetchUserProfile() {
  loading.value = true;
  errorMsg.value = "";

  const token = localStorage.getItem("token");
  if (!token) {
    router.push("/login");
    return;
  }

  try {
    // 从token中获取用户ID
    const userId = getUserIdFromToken(token);

    const apiUrl = `http://127.0.0.1:5000/api/user/${userId}?action=get_userinfo`;

    const res = await fetch(apiUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (!res.ok) {
      const errorText = await res.text();

      if (res.status === 401) {
        localStorage.removeItem("token");
        localStorage.removeItem("userInfo");
        router.push("/login");
        return;
      }
      throw new Error(`获取用户信息失败: ${res.status} ${errorText}`);
    }

    const data = await res.json();

    user.value = {
      ...data,
      // 确保有默认值
      avatar: data.avatar || "https://via.placeholder.com/100",
      solved: data.solved || 0,
      submitted: data.submitted || 0,
      accuracy: data.accuracy || 0,
    };
  } catch (err) {
    errorMsg.value = `获取用户信息失败: ${err.message}`;
  } finally {
    loading.value = false;
  }
}

// 更新用户名 - 使用统一的用户API
async function saveUsername() {
  if (!isUsernameValid.value) return;

  updatingUsername.value = true;

  const token = localStorage.getItem("token");
  if (!token) {
    router.push("/login");
    return;
  }

  // 清理用户名输入
  const sanitizedUsername = DOMPurify.sanitize(newUsername.value.trim());

  try {
    // 从token中获取用户ID
    const userId = getUserIdFromToken(token);
    const res = await fetch(`http://127.0.0.1:5000/api/user/${userId}`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        action: "update_username",
        username: sanitizedUsername,
      }),
    });

    if (!res.ok) {
      if (res.status === 401) {
        localStorage.removeItem("token");
        router.push("/login");
        return;
      }

      const errorData = await res.json();
      throw new Error(errorData.error || "更新用户名失败");
    }

    // 更新成功
    user.value.username = sanitizedUsername;
    showUsernameModal.value = false;
    newUsername.value = "";
  } catch (err) {
    console.error("更新用户名失败:", err);
    errorMsg.value = err.message || "更新用户名失败，请稍后重试";
  } finally {
    updatingUsername.value = false;
  }
}

// 更新头像 - 使用统一的用户API
async function saveAvatar() {
  if (!isAvatarValid.value) return;

  updatingAvatar.value = true;

  const token = localStorage.getItem("token");
  if (!token) {
    router.push("/login");
    return;
  }

  // 清理 URL 输入
  const sanitizedUrl = DOMPurify.sanitize(newAvatarUrl.value.trim());

  try {
    // 从token中获取用户ID
    const userId = getUserIdFromToken(token);
    const res = await fetch(`http://127.0.0.1:5000/api/user/${userId}`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        action: "update_avatar",
        avatar: sanitizedUrl,
      }),
    });

    if (!res.ok) {
      if (res.status === 401) {
        localStorage.removeItem("token");
        router.push("/login");
        return;
      }

      const errorData = await res.json();
      throw new Error(errorData.error || "更新头像失败");
    }

    // 更新成功
    user.value.avatar = sanitizedUrl;
    showAvatarModal.value = false;
    newAvatarUrl.value = "";
  } catch (err) {
    console.error("更新头像失败:", err);
    errorMsg.value = err.message || "更新头像失败，请稍后重试";
  } finally {
    updatingAvatar.value = false;
  }
}

function goHome() {
  router.push("/home");
}

function logout() {
  localStorage.removeItem("token");
  router.push("/login");
}

function cancelUsernameChange() {
  showUsernameModal.value = false;
  newUsername.value = "";
  usernameError.value = "";
}

function cancelAvatarChange() {
  showAvatarModal.value = false;
  newAvatarUrl.value = "";
  avatarError.value = "";
}

// 添加收藏夹相关状态
const favorites = ref([]);
const loadingFavorites = ref(false);

// 获取用户收藏夹
async function fetchFavorites() {
  loadingFavorites.value = true;

  const token = localStorage.getItem("token");
  if (!token) {
    router.push("/login");
    return;
  }

  try {
    // 从token中获取用户ID
    const userId = getUserIdFromToken(token);
    const res = await fetch(
      `http://127.0.0.1:5000/api/user/${userId}?action=get_collections`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (!res.ok) {
      if (res.status === 401) {
        localStorage.removeItem("token");
        router.push("/login");
        return;
      }
      throw new Error("获取收藏夹失败");
    }

    const data = await res.json();
    favorites.value = data;
  } catch (err) {
    console.error("获取收藏夹失败:", err);
    errorMsg.value = "获取收藏夹失败，请稍后重试";
  } finally {
    loadingFavorites.value = false;
  }
}

// 查看题目
function viewProblem(problemId) {
  router.push(`/problem/${problemId}`);
}

// 移除收藏
async function removeFavorite(problemId) {
  const token = localStorage.getItem("token");
  if (!token) {
    router.push("/login");
    return;
  }

  try {
    // 从token中获取用户ID
    const userId = getUserIdFromToken(token);
    const res = await fetch(
      `http://127.0.0.1:5000/api/user/${userId}?action=remove_collection`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          problem_id: problemId,
          user_id: userId,
        }),
      }
    );

    if (!res.ok) {
      if (res.status === 401) {
        localStorage.removeItem("token");
        router.push("/login");
        return;
      }
      throw new Error("移除收藏失败");
    }

    // 从列表中移除
    favorites.value = favorites.value.filter(
      (fav) => fav.problem_id !== problemId
    );
  } catch (err) {
    console.error("移除收藏失败:", err);
    errorMsg.value = "移除收藏失败，请稍后重试";
  }
}

// 添加提交记录相关状态
const submissions = ref([]);
const loadingSubmissions = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const totalSubmissions = ref(0);

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(totalSubmissions.value / pageSize.value) || 1;
});

// 获取用户提交记录
async function fetchSubmissions(page = 1) {
  loadingSubmissions.value = true;

  const token = localStorage.getItem("token");
  if (!token) {
    router.push("/login");
    return;
  }

  try {
    // 从token中获取用户ID
    const userId = getUserIdFromToken(token);
    const res = await fetch(
      `http://127.0.0.1:5000/api/user/${userId}/submissions?page=${page}&per_page=${pageSize.value}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (!res.ok) {
      if (res.status === 401) {
        localStorage.removeItem("token");
        router.push("/login");
        return;
      }
      throw new Error("获取提交记录失败");
    }

    const data = await res.json();
    submissions.value = data.submissions || [];
    totalSubmissions.value = data.total || 0;
    currentPage.value = page;
  } catch (err) {
    console.error("获取提交记录失败:", err);
    errorMsg.value = "获取提交记录失败，请稍后重试";
  } finally {
    loadingSubmissions.value = false;
  }
}

// 切换页码
function changePage(page) {
  if (page < 1 || page > totalPages.value) return;
  fetchSubmissions(page);
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return "未知时间";

  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
}

// 获取状态对应的CSS类
function getStatusClass(status) {
  switch (status.toLowerCase()) {
    case "accepted":
      return "status-success";
    case "wrong answer":
      return "status-error";
    case "time limit exceeded":
      return "status-warning";
    case "memory limit exceeded":
      return "status-warning";
    case "runtime error":
      return "status-error";
    case "compilation error":
      return "status-error";
    default:
      return "status-default";
  }
}

// 获取状态对应的显示文本
function getStatusText(status) {
  switch (status.toLowerCase()) {
    case "accepted":
      return "通过";
    case "wrong answer":
      return "答案错误";
    case "time limit exceeded":
      return "超时";
    case "memory limit exceeded":
      return "内存超限";
    case "runtime error":
      return "运行错误";
    case "compilation error":
      return "编译错误";
    default:
      return status;
  }
}

// 页面加载时获取用户信息、收藏夹和提交记录
onMounted(() => {
  fetchUserProfile();
  fetchFavorites();
  fetchSubmissions();
});

// 添加辅助函数来从token获取用户ID
function getUserIdFromToken(token) {
  // 实际应用中，您需要解析JWT token来获取用户ID
  // 这里简化处理，假设本地存储中有用户信息
  const userInfoStr = localStorage.getItem("userInfo");

  const userInfo = JSON.parse(userInfoStr || "{}");

  const userId = userInfo.id || 1; // 默认返回1，实际应用中应该返回真实的用户ID

  return userId;
}
</script>

<style scoped>
.user-profile {
  max-width: 800px;
  margin: 40px auto;
  padding: 30px;
  border-radius: 12px;
  background-color: var(--vt-c-white);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  font-family: Arial, sans-serif;
}

.header-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-bottom: 20px;
}

.header-actions button {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.profile-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.profile-card {
  width: 100%;
  margin-bottom: 30px;
  padding: 30px;
  background-color: var(--vt-c-white-soft);
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.profile-card:hover {
  transform: translateY(-5px);
}

.avatar-container {
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 4px solid #2575fc;
  object-fit: cover;
  box-shadow: 0 4px 10px rgba(37, 117, 252, 0.2);
  transition: transform 0.3s;
}

.avatar:hover {
  transform: scale(1.05);
}

.change-avatar-btn {
  position: absolute;
  bottom: 5px;
  left: 50%;
  transform: translateX(-50%);
  background: #2575fc;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.3s, background-color 0.3s;
}

.avatar-container:hover .change-avatar-btn {
  opacity: 1;
}

.change-avatar-btn:hover {
  background-color: #1a52d8;
}

.username-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 10px;
}

.username-container h2 {
  margin: 0;
  color: var(--color-heading);
  font-size: 1.8rem;
}

.edit-btn {
  background: none;
  border: none;
  color: #2575fc;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: color 0.3s;
}

.edit-btn:hover {
  color: #1a52d8;
}

.join-date {
  margin: 8px 0 20px;
  color: #666;
  font-size: 0.9rem;
}

.stats {
  display: flex;
  justify-content: space-around;
  margin: 20px 0;
}

.stat-item {
  padding: 15px 25px;
  border-radius: 8px;
  background-color: var(--vt-c-white);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-item h3 {
  margin: 0;
  font-size: 28px;
  color: #2575fc;
  font-weight: 700;
}

.stat-item p {
  margin: 8px 0 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  width: 100%;
}

.actions button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.home-btn {
  background-color: #f5f5f5;
  color: #333;
}

.home-btn:hover {
  background-color: #e0e0e0;
  transform: translateY(-2px);
}

.logout-btn {
  background-color: #2575fc;
  color: white;
}

.logout-btn:hover {
  background-color: #1a52d8;
  transform: translateY(-2px);
}

.actions button:active {
  transform: translateY(0);
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: white;
  padding: 30px;
  border-radius: 12px;
  width: 350px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-content h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2575fc;
  font-size: 1.5rem;
  text-align: center;
}

.form-group {
  margin-bottom: 20px;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.form-group input {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus {
  border-color: #2575fc;
  outline: none;
  box-shadow: 0 0 5px rgba(37, 117, 252, 0.5);
}

.input-error {
  border-color: #f56c6c !important;
  box-shadow: 0 0 5px rgba(245, 108, 108, 0.5) !important;
}

.error-text {
  color: #f56c6c;
  font-size: 12px;
  margin: 5px 0 0;
}

.avatar-preview {
  margin: 15px 0;
  text-align: center;
}

.avatar-preview img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #2575fc;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modal-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 25px;
}

.modal-actions button {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 80px;
}

.cancel-btn {
  background-color: #f2f2f2;
  color: #333;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}

.save-btn {
  background-color: #2575fc;
  color: white;
  position: relative;
}

.save-btn:hover {
  background-color: #1a52d8;
}

.save-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.save-btn:disabled:hover {
  background-color: #cccccc;
  transform: none;
}

.loading-overlay {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-spinner,
.btn-spinner {
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
  display: inline-block;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(37, 117, 252, 0.2);
  border-top-color: #2575fc;
  margin-bottom: 15px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-message {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.retry-btn {
  background-color: #d32f2f;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.retry-btn:hover {
  background-color: #b71c1c;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 收藏夹样式 */
.favorites-section {
  margin-top: 30px;
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.favorites-section h2 {
  margin-bottom: 20px;
  color: #333;
  font-size: 1.5rem;
}

.empty-favorites {
  text-align: center;
  padding: 30px;
  color: #888;
  font-style: italic;
}

.favorites-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.favorite-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
}

.favorite-item:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.favorite-info h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.favorite-date {
  margin: 5px 0 0;
  font-size: 0.8rem;
  color: #888;
}

.favorite-actions {
  display: flex;
  gap: 10px;
}

.view-btn,
.remove-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-btn {
  background-color: #4caf50;
  color: white;
}

.view-btn:hover {
  background-color: #45a049;
}

.remove-btn {
  background-color: #f44336;
  color: white;
}

.remove-btn:hover {
  background-color: #d32f2f;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  padding: 30px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #3498db;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 添加提交记录相关样式 */
.submissions-section {
  width: 100%;
  margin-top: 30px;
  padding: 30px;
  background-color: var(--vt-c-white-soft);
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.submissions-section h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: var(--color-heading);
  font-size: 1.5rem;
  text-align: center;
}

.empty-submissions {
  text-align: center;
  padding: 30px;
  color: #888;
  font-style: italic;
}

.submissions-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.submissions-table th,
.submissions-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.submissions-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.submission-item:hover {
  background-color: #f8f9fa;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-success {
  background-color: #e6f7e6;
  color: #28a745;
}

.status-error {
  background-color: #fbe9e7;
  color: #dc3545;
}

.status-warning {
  background-color: #fff3e0;
  color: #fd7e14;
}

.status-default {
  background-color: #e9ecef;
  color: #6c757d;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.page-btn {
  background-color: #f0f0f0;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.page-btn:hover:not(:disabled) {
  background-color: #e0e0e0;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  margin: 0 15px;
  font-size: 0.9rem;
  color: #666;
}
</style>

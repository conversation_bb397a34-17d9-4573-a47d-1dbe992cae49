<!DOCTYPE html>
<html>
  <head>
    <title>测试前端API调用</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  </head>
  <body>
    <h1>测试前端API调用</h1>
    <button onclick="testSolutionsAPI()">测试题解API</button>
    <button onclick="testWithoutAxios()">测试原生fetch</button>
    <button onclick="clearCache()">清除缓存</button>
    <div id="result"></div>

    <script>
      async function testSolutionsAPI() {
        const resultDiv = document.getElementById("result");
        resultDiv.innerHTML = "正在测试...";

        try {
          // 设置测试token
          localStorage.setItem("token", "test_token");

          console.log("开始测试题解API...");

          const response = await axios.get(
            "http://127.0.0.1:5000/api/solutions",
            {
              params: {
                pg: 1,
                ppg: 10,
              },
              headers: {
                Authorization: "Bearer test_token",
              },
            }
          );

          console.log("API响应:", response);

          resultDiv.innerHTML = `
                    <h3>✅ API调用成功</h3>
                    <p>状态码: ${response.status}</p>
                    <p>数据类型: ${typeof response.data}</p>
                    <pre>${JSON.stringify(response.data, null, 2)}</pre>
                `;
        } catch (error) {
          console.error("API调用失败:", error);

          let errorMsg = "未知错误";
          if (error.response) {
            errorMsg = `状态码: ${error.response.status}, 错误: ${
              error.response.data?.error || "服务器错误"
            }`;
          } else if (error.request) {
            errorMsg = "网络错误，无法连接到服务器";
          } else {
            errorMsg = error.message;
          }

          resultDiv.innerHTML = `
                    <h3>❌ API调用失败</h3>
                    <p>${errorMsg}</p>
                `;
        }
      }

      async function testWithoutAxios() {
        const resultDiv = document.getElementById("result");
        resultDiv.innerHTML = "正在使用原生fetch测试...";

        try {
          const response = await fetch(
            "http://127.0.0.1:5000/api/solutions?pg=1&ppg=10",
            {
              method: "GET",
              headers: {
                Authorization: "Bearer test_token",
                "Content-Type": "application/json",
              },
            }
          );

          console.log("Fetch响应:", response);

          if (response.ok) {
            const data = await response.json();
            console.log("Fetch数据:", data);

            resultDiv.innerHTML = `
                      <h3>✅ 原生fetch调用成功</h3>
                      <p>状态码: ${response.status}</p>
                      <p>数据类型: ${typeof data}</p>
                      <pre>${JSON.stringify(data, null, 2)}</pre>
                  `;
          } else {
            const errorText = await response.text();
            resultDiv.innerHTML = `
                      <h3>❌ 原生fetch调用失败</h3>
                      <p>状态码: ${response.status}</p>
                      <p>错误: ${errorText}</p>
                  `;
          }
        } catch (error) {
          console.error("Fetch错误:", error);
          resultDiv.innerHTML = `
                  <h3>❌ 原生fetch调用失败</h3>
                  <p>错误: ${error.message}</p>
              `;
        }
      }

      function clearCache() {
        localStorage.clear();
        sessionStorage.clear();
        if ("caches" in window) {
          caches.keys().then((names) => {
            names.forEach((name) => {
              caches.delete(name);
            });
          });
        }
        document.getElementById("result").innerHTML = "✅ 缓存已清除";
      }
    </script>
  </body>
</html>

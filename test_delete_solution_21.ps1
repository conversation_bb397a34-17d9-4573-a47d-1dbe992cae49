# 测试删除题解ID 21
Write-Host "测试删除题解ID 21..."

$deleteBody = @{
    ans_id = 21
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/solutions?action=remove_solutions" -Method POST -Headers @{'Authorization'='Bearer test_token'; 'Content-Type'='application/json'} -Body $deleteBody
    Write-Host "✅ 删除题解测试: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "❌ 删除题解测试失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

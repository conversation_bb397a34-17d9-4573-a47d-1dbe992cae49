# 基础API测试
Write-Host "测试基础API..."

# 测试服务器状态
try {
    Write-Host "1. 测试服务器状态..."
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/test" -Method GET
    Write-Host "✅ 服务器正常: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "❌ 服务器测试失败: $($_.Exception.Message)"
}

Write-Host ""

# 测试数据库连接
try {
    Write-Host "2. 测试数据库连接..."
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/test/db" -Method GET
    Write-Host "✅ 数据库连接正常: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "❌ 数据库测试失败: $($_.Exception.Message)"
}

Write-Host ""

# 测试题目过滤API（不需要token）
try {
    Write-Host "3. 测试题目过滤API..."
    $body = '{"page": 1, "per_page": 5}'
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/questions/filter" -Method POST -Headers @{'Content-Type'='application/json'} -Body $body
    Write-Host "✅ 题目过滤API正常: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "❌ 题目过滤API失败: $($_.Exception.Message)"
}

"""
测试用例管理
为不同题目提供测试用例
"""

# 示例测试用例数据
TEST_CASES = {
    # 题目ID: 测试用例列表
    1: [  # 两数之和
        {
            'input': '2 7 11 15\n9',
            'expected_output': '0 1'
        },
        {
            'input': '3 2 4\n6',
            'expected_output': '1 2'
        },
        {
            'input': '3 3\n6',
            'expected_output': '0 1'
        }
    ],
    2: [  # 有效的括号
        {
            'input': '()',
            'expected_output': 'true'
        },
        {
            'input': '()[]{}',
            'expected_output': 'true'
        },
        {
            'input': '(]',
            'expected_output': 'false'
        },
        {
            'input': '([)]',
            'expected_output': 'false'
        },
        {
            'input': '{[]}',
            'expected_output': 'true'
        }
    ],
    3: [  # 简单的加法
        {
            'input': '1 2',
            'expected_output': '3'
        },
        {
            'input': '10 20',
            'expected_output': '30'
        },
        {
            'input': '-5 5',
            'expected_output': '0'
        }
    ]
}

def get_test_cases(question_id: int):
    """获取指定题目的测试用例"""
    return TEST_CASES.get(question_id, [])

def add_test_case(question_id: int, input_data: str, expected_output: str):
    """添加测试用例"""
    if question_id not in TEST_CASES:
        TEST_CASES[question_id] = []
    
    TEST_CASES[question_id].append({
        'input': input_data,
        'expected_output': expected_output
    })

def remove_test_case(question_id: int, case_index: int):
    """删除测试用例"""
    if question_id in TEST_CASES and 0 <= case_index < len(TEST_CASES[question_id]):
        del TEST_CASES[question_id][case_index]
        return True
    return False

def get_all_test_cases():
    """获取所有测试用例"""
    return TEST_CASES

# 测试删除题目API
Write-Host "测试删除题目API..."

# 测试删除一个不存在的题目
try {
    Write-Host "1. 测试删除不存在的题目..."
    $body = '{"id": 999999}'
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/admin/questions?action=remove_question" -Method POST -Headers @{'Authorization'='Bearer test_token'; 'Content-Type'='application/json'} -Body $body
    Write-Host "✅ 删除不存在题目测试: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "❌ 删除不存在题目测试失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

Write-Host ""

# 测试删除空ID
try {
    Write-Host "2. 测试删除空ID..."
    $body = '{}'
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/admin/questions?action=remove_question" -Method POST -Headers @{'Authorization'='Bearer test_token'; 'Content-Type'='application/json'} -Body $body
    Write-Host "✅ 删除空ID测试: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "❌ 删除空ID测试失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

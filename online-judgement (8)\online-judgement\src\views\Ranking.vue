<template>
  <div class="ranking-page">
    <div class="back-home">
      <button @click="goHome">← 返回主页</button>
    </div>

    <h1 class="page-title">用户排行榜</h1>

    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      <p>加载中...</p>
    </div>

    <div v-else-if="error" class="error-message">
      {{ error }}
      <button @click="fetchRanking" class="retry-btn">重试</button>
    </div>

    <div v-else class="ranking-container">
      <div class="ranking-header">
        <div class="rank-column">排名</div>
        <div class="user-column">用户</div>
        <div class="solved-column">已解决</div>
        <div class="accuracy-column">正确率</div>
      </div>

      <div
        v-for="(user, index) in rankingList"
        :key="user.id"
        class="ranking-row"
      >
        <div class="rank-column">
          <div :class="['rank-badge', getRankClass(user.rank || index + 1)]">
            {{ user.rank || index + 1 }}
          </div>
        </div>
        <div class="user-column">
          <span class="username">{{ user.username }}</span>
        </div>
        <div class="solved-column">{{ user.solved }}</div>
        <div class="accuracy-column">{{ user.accuracy }}%</div>
      </div>

      <div v-if="rankingList.length === 0" class="no-data">暂无排名数据</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import axios from "axios";

const router = useRouter();
const loading = ref(true);
const error = ref("");
const rankingList = ref([]);

function goHome() {
  router.push("/home");
}

function getRankClass(rank) {
  if (rank === 1) return "rank-first";
  if (rank === 2) return "rank-second";
  if (rank === 3) return "rank-third";
  return "";
}

async function fetchRanking() {
  loading.value = true;
  error.value = "";

  try {
    const response = await axios.get("http://127.0.0.1:5000/api/rank");

    if (response.status === 200) {
      // 将后端数据映射到前端需要的格式
      rankingList.value = response.data.map((user) => ({
        id: user.rank, // 使用rank作为id
        username: user.username,
        solved: user.solved_count,
        accuracy: Math.round(user.pass_rate), // 后端已经是百分比，不需要再乘100
      }));
    } else {
      throw new Error("获取排行榜失败");
    }
  } catch (err) {
    error.value = "获取排行榜失败，请稍后重试";
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  fetchRanking();
});
</script>

<style scoped>
.ranking-page {
  padding: 2rem;
  max-width: 1000px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.back-home {
  margin-bottom: 2rem;
}

.back-home button {
  background-color: #409eff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.back-home button:hover {
  background-color: #66b1ff;
}

.page-title {
  text-align: center;
  margin-bottom: 2rem;
  color: #303133;
  font-size: 2rem;
  font-weight: 600;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #409eff;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-message {
  text-align: center;
  color: #f56c6c;
  padding: 2rem;
  background-color: #fef0f0;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.retry-btn {
  background-color: #f56c6c;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  margin-top: 1rem;
  cursor: pointer;
}

.ranking-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.ranking-header {
  display: flex;
  background-color: #f5f7fa;
  padding: 1rem 1.5rem;
  font-weight: 600;
  color: #606266;
  border-bottom: 1px solid #ebeef5;
}

.ranking-row {
  display: flex;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #ebeef5;
  transition: background-color 0.3s;
}

.ranking-row:hover {
  background-color: #f5f7fa;
}

.ranking-row:last-child {
  border-bottom: none;
}

.rank-column {
  flex: 0 0 80px;
  display: flex;
  align-items: center;
}

.user-column {
  flex: 1;
  display: flex;
  align-items: center;
  padding-left: 20px; /* 添加左侧内边距，使用户名不会紧贴左边 */
}

.solved-column {
  flex: 0 0 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #409eff;
}

.accuracy-column {
  flex: 0 0 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #67c23a;
}

.rank-badge {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #909399;
  color: white;
  font-weight: 700;
}

.rank-first {
  background: linear-gradient(135deg, #ffd700, #ffb700);
  box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
}

.rank-second {
  background: linear-gradient(135deg, #c0c0c0, #a0a0a0);
  box-shadow: 0 4px 8px rgba(192, 192, 192, 0.3);
}

.rank-third {
  background: linear-gradient(135deg, #cd7f32, #b06728);
  box-shadow: 0 4px 8px rgba(205, 127, 50, 0.3);
}

/* 删除以下样式 */
/* .user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 1rem;
  object-fit: cover;
  border: 2px solid #ebeef5;
} */

.username {
  font-weight: 600;
  color: #303133;
}

.no-data {
  padding: 3rem 0;
  text-align: center;
  color: #909399;
  font-size: 1.1rem;
}
</style>

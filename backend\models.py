from flask import Flask,request,jsonify
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import UnicodeText
from enum import Enum
from datetime import datetime, timezone
from database import db


class OperationResult(Enum):
    """操作结果枚举"""
    SUCCESS = 'success'
    FAILURE = 'failure'

class Users(db.Model):
    __tablename__='Users'
    UserID = db.Column(db.Integer, primary_key=True,autoincrement=True)
    UserName = db.Column(db.String(100), nullable=False)
    Password = db.Column(db.String(255), nullable=False)
    Status = db.Column(db.String(20), default='active')
    Level = db.Column(db.String(20),nullable=False, default='user')
    UserPict = db.Column(db.Text)
    RegistrationTime = db.Column(db.DateTime, default=datetime.now(timezone.utc))

class Questions(db.Model):
    __tablename__ = 'Questions'

    QuesID = db.Column(db.Integer, primary_key=True, autoincrement=True)  # IDENTITY(1,1)
    QuesType = db.Column(db.String(20), nullable=False)
    QuesLevel = db.Column(db.String(20), nullable=False)
    QuesName = db.Column(db.String(100), nullable=False)
    QuesDescrip = db.Column(db.String(1024), nullable=False)
    IptFomart =  db.Column(db.String(100), nullable=False)
    OptFomart =  db.Column(db.String(100), nullable=False)
    IptExample = db.Column(UnicodeText)
    OptExample = db.Column(UnicodeText)
    SubNum = db.Column(db.Integer, default=0)  # 默认值0
    CollNum = db.Column(db.Integer, default=0)  # 默认值0
    PassNum = db.Column(db.Integer, default=0)  # 默认值0

class Answers(db.Model):

    AnsID = db.Column( db.Integer, primary_key=True, autoincrement=True)  # IDENTITY(1,1)
    QuesID = db.Column( db.Integer, db.ForeignKey('Questions.QuesID'), nullable=False)
    AnsTitle =db.Column( db.Text)
    Content = db.Column( db.Text)
    AnsStatus=db.Column(db.String(20),nullable=False, default='pending')
    TIME = db.Column(db.DateTime, default=datetime.now(timezone.utc))  # 替换GETDATE()
    UserID = db.Column( db.Integer, db.ForeignKey('Users.UserID'), nullable=False)

    # 定义关系（relationship）
    question = db.relationship('Questions', backref=db.backref('answers', lazy='dynamic'))
    user = db.relationship('Users', backref=db.backref('answers', lazy='dynamic'))

class Collections(db.Model):

    # 复合主键
    QuesID = db.Column('QuesID', db.Integer, db.ForeignKey('Questions.QuesID'), primary_key=True)
    UserID = db.Column('UserID', db.Integer, db.ForeignKey('Users.UserID'), primary_key=True)

    # 其他字段
    CollTime = db.Column('CollTime', db.DateTime, default=datetime.now(timezone.utc))  # 替换GETDATE()
    QuesRemark = db.Column('QuesRemark', db.String(100))

    # 定义关系
    question = db.relationship('Questions', backref=db.backref('collections', lazy='dynamic'))
    user = db.relationship('Users', backref=db.backref('collections', lazy='dynamic'))

class Submissions(db.Model):

    SubID = db.Column(db.Integer, primary_key=True, autoincrement=True)  # IDENTITY(1,1)
    UserID = db.Column(db.Integer, db.ForeignKey('Users.UserID'), nullable=False)
    QuesID = db.Column(db.Integer, db.ForeignKey('Questions.QuesID'), nullable=False)
    SubContent = db.Column(db.Text)
    SubTime = db.Column(db.DateTime, default=datetime.now(timezone.utc))  # 替换GETDATE()
    PassStatus = db.Column(db.String(20), nullable=False, default='Judging')
    Language = db.Column(db.String(20), default='python')  # 编程语言
    JudgeResult = db.Column(db.Text)  # 判题结果消息
    JudgeDetails = db.Column(db.Text)  # 详细判题结果
    ExecutionTime = db.Column(db.Integer)  # 执行时间(ms)
    MemoryUsage = db.Column(db.Integer)  # 内存使用(KB)

    # 定义关系
    user = db.relationship('Users',backref=db.backref('submissions', lazy='dynamic'))
    question = db.relationship('Questions',backref=db.backref('submissions', lazy='dynamic'))

class Logs(db.Model):

    LogID = db.Column(db.Integer, primary_key=True, autoincrement=True)  # IDENTITY(1,1)
    UserID = db.Column(db.Integer, db.ForeignKey('Users.UserID'), nullable=False)
    OperType = db.Column(db.String(20))  # 操作类型
    OperRes = db.Column(db.String(20))   # 操作结果
    LogTime = db.Column(db.DateTime, default=datetime.now(timezone.utc))  # 替换GETDATE()

    # 定义关系
    user = db.relationship('Users', backref=db.backref('logs', lazy='dynamic'))

class ReviewsFeedback(db.Model):

    ReviFbID = db.Column('ReviFbID', db.Integer, primary_key=True, autoincrement=True)  # IDENTITY(1,1)
    ReviID = db.Column('ReviID', db.Integer, db.ForeignKey('Reviews.ReviID'), nullable=False)
    FbTime = db.Column('FbTime', db.DateTime, default=datetime.now(timezone.utc))  # 替换GETDATE()
    FbCont = db.Column('FbCont', db.Text)  # 反馈内容
    UserID = db.Column('UserID', db.Integer, db.ForeignKey('Users.UserID'), nullable=False)

    # 定义关系
    review = db.relationship('Reviews', backref=db.backref('feedbacks', lazy='dynamic'))
    user = db.relationship('Users', backref=db.backref('review_feedbacks', lazy='dynamic'))

class QuestionsFeedback(db.Model):

    QuesFbID = db.Column('QuesFbID', db.Integer, primary_key=True, autoincrement=True)  # IDENTITY(1,1)
    QuesID = db.Column('QuesID', db.Integer, db.ForeignKey('Questions.QuesID'), nullable=False)
    FbTime = db.Column('FbTime', db.DateTime, default=datetime.now(timezone.utc))  # 替换GETDATE()
    FbCont = db.Column('FbCont', db.Text)  # 反馈内容
    UserID = db.Column('UserID', db.Integer, db.ForeignKey('Users.UserID'), nullable=False)

    # 定义关系
    question = db.relationship('Questions', backref=db.backref('feedbacks', lazy='dynamic'))
    user = db.relationship('Users', backref=db.backref('question_feedbacks', lazy='dynamic'))

class Reviews(db.Model):
    __tablename__ = 'Reviews'

    ReviID = db.Column(db.Integer, primary_key=True, autoincrement=True)  # IDENTITY(1,1)
    UserID = db.Column(db.Integer, db.ForeignKey('Users.UserID'), nullable=False)
    QuesID = db.Column(db.Integer, db.ForeignKey('Questions.QuesID'))
    ParentReviID = db.Column(db.Integer, db.ForeignKey('Reviews.ReviID'))
    ReviCont = db.Column(db.Text)
    ReviDate = db.Column(db.DateTime, default=datetime.now(timezone.utc))

    # 定义关系
    user = db.relationship('Users', backref=db.backref('reviews', lazy='dynamic'))
    question = db.relationship('Questions', backref=db.backref('reviews', lazy='dynamic'))

    # 自引用关系（评论的回复）
    parent_review = db.relationship('Reviews', remote_side=[ReviID], backref=db.backref('replies', lazy='dynamic'))
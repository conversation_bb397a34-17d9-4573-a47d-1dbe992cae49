# 简单测试题解API
Write-Host "测试题解API..."

# 测试题目73的题解
Write-Host "测试题目73的题解..."
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/answers/73" -Method GET
    Write-Host "状态码: $($response.StatusCode)"
    Write-Host "响应内容:"
    Write-Host $response.Content
} catch {
    Write-Host "错误: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)"
    }
}

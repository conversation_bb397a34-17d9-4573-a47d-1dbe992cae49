<template>
  <div class="table-container">
    <table class="problem-table">
      <thead>
        <tr>
          <th>题目</th>
          <th>难度</th>
          <th>标签</th>
          <th>通过/提交</th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="(problem, index) in problems"
          :key="problem.id"
          class="clickable-row"
          @click="goToProblem(problem.id)"
        >
          <td>{{ index + 1 }}. {{ problem.title }}</td>
          <td>{{ problem.difficulty }}</td>
          <td>
            <span
              v-for="tag in getTagsArray(problem.tags)"
              :key="tag"
              class="tag"
            >
              {{ tag }}
            </span>
          </td>
          <td>{{ problem.accepted }} / {{ problem.submitted }}</td>
        </tr>
        <tr v-if="problems.length === 0">
          <td colspan="4" style="text-align: center; color: #888">
            暂无题目数据
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { defineProps } from "vue";
import { useRouter } from "vue-router";

const props = defineProps({
  problems: {
    type: Array,
    default: () => [],
  },
});

const router = useRouter();

function goToProblem(id) {
  router.push(`/problem/${id}`);
}
</script>

<style scoped>
.table-container {
  overflow-x: auto;
  background: #fff;
  padding: 1rem 1.5rem;
  border-radius: 16px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.07);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica,
    Arial, sans-serif;
}

.problem-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 8px;
  min-width: 700px;
}

th,
td {
  text-align: left;
  padding: 1rem 1.25rem;
  vertical-align: middle;
}

th {
  background-color: #f3f6f9;
  color: #4a4a4a;
  font-weight: 700;
  font-size: 1rem;
  user-select: none;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

tbody tr {
  background-color: #fafafa;
  border-radius: 12px;
  box-shadow: 0 0 8px rgb(0 0 0 / 0.03);
  transition: background-color 0.3s ease;
  cursor: pointer;
}

tbody tr:nth-child(even) {
  background-color: #f0f2f5;
}

tbody tr:hover {
  background-color: #e6f2ff;
  box-shadow: 0 0 12px rgb(0 102 255 / 0.3);
}

td:first-child {
  font-weight: 600;
  color: #222;
}

td {
  font-size: 0.95rem;
  color: #555;
  white-space: nowrap;
}

.clickable-row {
  user-select: none;
}
</style>

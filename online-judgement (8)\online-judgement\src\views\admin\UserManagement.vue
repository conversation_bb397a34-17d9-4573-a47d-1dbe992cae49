<template>
  <div class="user-management">
    <h2>用户管理</h2>

    <!-- 搜索区域 -->
    <div class="search-container">
      <el-input
        v-model="searchQuery"
        placeholder="输入用户名或ID搜索"
        class="search-input"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <el-button type="primary" @click="searchUser" :loading="searching">
        搜索
      </el-button>
    </div>

    <!-- 用户信息卡片 -->
    <div v-if="currentUser" class="user-card">
      <div class="user-header">
        <h3>用户信息</h3>
      </div>
      <div class="user-info">
        <div class="info-item">
          <span class="label">ID:</span>
          <span>{{ currentUser.id }}</span>
        </div>
        <div class="info-item">
          <span class="label">用户名:</span>
          <span>{{ currentUser.username }}</span>
        </div>
        <div class="info-item">
          <span class="label">注册日期:</span>
          <span>{{ currentUser.joinDate }}</span>
        </div>
        <div class="info-item">
          <span class="label">状态:</span>
          <el-tag
            :type="currentUser.status === 'active' ? 'success' : 'danger'"
          >
            {{ currentUser.status === "active" ? "正常" : "禁用" }}
          </el-tag>
        </div>
      </div>
      <div class="user-actions">
        <el-button
          type="danger"
          @click="toggleUserStatus(currentUser)"
          :disabled="loading"
        >
          {{ currentUser.status === "active" ? "禁用用户" : "启用用户" }}
        </el-button>
      </div>
    </div>

    <!-- 搜索结果列表 -->
    <div v-else-if="searchResults.length > 0" class="search-results">
      <h3>搜索结果</h3>
      <el-table :data="searchResults" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag
              :type="scope.row.status === 'active' ? 'success' : 'danger'"
            >
              {{ scope.row.status === "active" ? "正常" : "禁用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="joinDate" label="注册日期" width="180" />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="selectUser(scope.row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 无结果提示 -->
    <div v-else-if="searched && !loading" class="no-results">
      <el-empty description="未找到匹配的用户" />
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search } from "@element-plus/icons-vue";

// 状态变量
const searchQuery = ref("");
const searchResults = ref([]);
const currentUser = ref(null);
const loading = ref(false);
const searching = ref(false);
const searched = ref(false);

// 模拟用户数据库
const usersDatabase = [
  {
    id: 2,
    username: "user1",
    type: "user",
    status: "active",
    joinDate: "2023-01-15",
  },
  {
    id: 3,
    username: "user2",
    type: "user",
    status: "banned",
    joinDate: "2023-02-20",
  },
  {
    id: 4,
    username: "user3",
    type: "user",
    status: "active",
    joinDate: "2023-03-05",
  },
  {
    id: 5,
    username: "test_user",
    type: "user",
    status: "active",
    joinDate: "2023-04-10",
  },
];

// 搜索用户
async function searchUser() {
  if (!searchQuery.value.trim()) {
    ElMessage.warning("请输入搜索内容");
    return;
  }

  searching.value = true;
  loading.value = true;
  currentUser.value = null;
  searchResults.value = [];

  try {
    // 从本地存储获取token
    const token = localStorage.getItem("token");
    if (!token) {
      ElMessage.error("您的登录已过期，请重新登录");
      return;
    }

    // 准备请求数据
    const searchData = {
      username: searchQuery.value.trim(),
    };

    // 发送POST请求到后端API
    const response = await fetch(
      `http://127.0.0.1:5000/api/admin/users/search`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(searchData),
      }
    );

    // 处理响应
    if (!response.ok) {
      if (response.status === 401) {
        ElMessage.error("您的登录已过期，请重新登录");
        return;
      }
      throw new Error("搜索请求失败");
    }

    const data = await response.json();
    searchResults.value = data.users || [];

    searched.value = true;

    // 显示搜索结果
    if (searchResults.value.length === 0) {
      ElMessage.info("未找到匹配的用户");
    } else if (searchResults.value.length === 1) {
      // 如果只有一个结果，直接显示详情
      selectUser(searchResults.value[0]);
      ElMessage.success(`找到用户: ${searchResults.value[0].username}`);
    } else {
      ElMessage.success(`找到 ${searchResults.value.length} 个用户`);
    }
  } catch (error) {
    console.error("搜索用户失败:", error);
    ElMessage.error("搜索功能失效，请稍后重试");

    // 开发环境下使用模拟数据（可在生产环境移除）
    if (process.env.NODE_ENV === "development") {
      console.log("使用模拟数据");
      const query = searchQuery.value.toLowerCase();
      searchResults.value = usersDatabase.filter(
        (user) =>
          user.username.toLowerCase().includes(query) ||
          user.id.toString() === query
      );

      if (searchResults.value.length === 0) {
        ElMessage.info("未找到匹配的用户");
      } else if (searchResults.value.length === 1) {
        selectUser(searchResults.value[0]);
        ElMessage.success(`找到用户: ${searchResults.value[0].username}`);
      } else {
        ElMessage.success(`找到 ${searchResults.value.length} 个用户`);
      }
    }
  } finally {
    searching.value = false;
    loading.value = false;
  }
}

// 选择用户查看详情
function selectUser(user) {
  currentUser.value = { ...user };
  searchResults.value = [];
}

// 切换用户状态（禁用/启用）
function toggleUserStatus(user) {
  const newStatus = user.status === "active" ? "banned" : "active";
  const action = user.status === "active" ? "禁用" : "启用";

  ElMessageBox.confirm(
    `确定要${action}用户 "${user.username}" 吗?`,
    "确认操作",
    { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning" }
  )
    .then(async () => {
      loading.value = true;

      try {
        // 从本地存储获取token
        const token = localStorage.getItem("token");
        if (!token) {
          ElMessage.error("您的登录已过期，请重新登录");
          return;
        }

        // 准备请求数据
        const statusData = {
          username: user.username,
          status: newStatus,
        };

        // 发送请求到后端API
        const response = await fetch(
          "http://127.0.0.1:5000/api/admin/users/status",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify(statusData),
          }
        );

        // 处理响应
        if (!response.ok) {
          if (response.status === 401) {
            ElMessage.error("您的登录已过期，请重新登录");
            return;
          }
          throw new Error("操作请求失败");
        }

        // 更新本地状态
        currentUser.value.status = newStatus;

        ElMessage.success(`已${action}用户: ${user.username}`);
      } catch (error) {
        ElMessage.error(`${action}用户失败，请稍后重试`);

        // 开发环境下使用模拟数据（可在生产环境移除）
        if (process.env.NODE_ENV === "development") {
          // 更新本地状态
          currentUser.value.status = newStatus;

          // 同时更新模拟数据库中的状态
          const userInDb = usersDatabase.find((u) => u.id === user.id);
          if (userInDb) userInDb.status = newStatus;

          ElMessage.success(`已${action}用户: ${user.username}`);
        }
      } finally {
        loading.value = false;
      }
    })
    .catch(() => {
      ElMessage.info("已取消操作");
    });
}
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.search-container {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.search-input {
  max-width: 300px;
}

.user-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.user-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.user-header h3 {
  margin: 0;
  color: #333;
}

.user-info {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: bold;
  margin-right: 10px;
  color: #666;
  min-width: 80px;
}

.user-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.search-results {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.search-results h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.no-results {
  margin-top: 40px;
  text-align: center;
}

.loading-container {
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}
</style>

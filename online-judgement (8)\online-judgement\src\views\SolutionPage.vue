<template>
  <div class="solution-page">
    <!-- 导航栏 -->
    <div class="navigation-bar">
      <button @click="goBack" class="back-button">← 返回题目</button>
      <button @click="goHome" class="home-button">返回主页</button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>正在加载题解...</p>
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <!-- 无题解提示 -->
    <div
      v-if="!loading && !error && solutions.length === 0"
      class="no-solutions"
    >
      <p>暂无题解，请等待官方或其他用户发布题解。</p>
    </div>

    <!-- 题解列表 -->
    <div
      v-if="!loading && !error && solutions.length > 0"
      class="solutions-container"
    >
      <h2>《{{ problemTitle }}》的题解</h2>

      <!-- 题解卡片列表 -->
      <div class="solution-list">
        <div
          v-for="solution in solutions"
          :key="solution.id"
          class="solution-card"
        >
          <div class="solution-header">
            <h3 class="solution-title">{{ solution.AnsTitle }}</h3>
            <div class="solution-meta">
              <span class="solution-author">作者: {{ solution.UserID }}</span>
              <span class="solution-time"
                >发布于: {{ formatDate(solution.TIME) }}</span
              >
            </div>
          </div>

          <!-- 题解内容区域 - 带展开/收起功能 -->
          <div class="solution-content-container">
            <div
              class="solution-content markdown-body"
              :class="{
                collapsed:
                  !expandedSolutions.includes(solution.id) &&
                  isLongContent(solution.Content),
              }"
              v-html="renderMarkdown(solution.Content)"
            ></div>

            <!-- 展开/收起按钮 -->
            <button
              v-if="isLongContent(solution.Content)"
              @click="toggleExpand(solution.id)"
              class="expand-button"
            >
              {{
                expandedSolutions.includes(solution.id) ? "收起" : "展开全文"
              }}
            </button>
          </div>

          <div class="solution-actions">
            <button @click="showComments(solution.id)" class="comment-button">
              <i class="icon-comment"></i> 评论 ({{ solution.comments || 0 }})
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import axios from "axios";
import { marked } from "marked";
import DOMPurify from "dompurify";

const router = useRouter();
const route = useRoute();
const problemId = route.params.id;

// 状态变量
const loading = ref(true);
const error = ref("");
const solutions = ref([]);
const problemTitle = ref("");
const expandedSolutions = ref([]); // 跟踪已展开的题解

// 导航方法
function goBack() {
  router.push(`/problem/${problemId}`);
}

function goHome() {
  router.push("/home");
}

// 获取题解列表
async function fetchSolutions() {
  loading.value = true;
  error.value = "";

  try {
    console.log(`正在获取题目 ${problemId} 的题解...`);
    const response = await axios.get(
      `http://127.0.0.1:5000/api/answers/${problemId}`
    );

    console.log("API响应:", response.data);

    if (response.status === 200) {
      // 后端直接返回数组，不是包含solutions字段的对象
      if (Array.isArray(response.data)) {
        solutions.value = response.data;
        // 如果有题解，尝试获取题目标题
        if (solutions.value.length > 0) {
          await fetchProblemTitle();
        } else {
          problemTitle.value = "未知题目";
        }
      } else {
        // 如果返回的是对象格式（兼容性处理）
        solutions.value = response.data.solutions || [];
        problemTitle.value = response.data.problemTitle || "未知题目";
      }
    } else {
      throw new Error("获取题解失败");
    }
  } catch (err) {
    console.error("获取题解失败:", err);

    // 详细错误处理
    if (err.response) {
      const status = err.response.status;
      if (status === 404) {
        error.value = "该题目暂无题解";
      } else {
        error.value = `获取题解失败: ${
          err.response.data?.error || err.message
        }`;
      }
    } else if (err.request) {
      error.value = "网络错误，请检查连接";
    } else {
      error.value = "获取题解失败，请稍后重试";
    }
  } finally {
    loading.value = false;
  }
}

// 获取题目标题
async function fetchProblemTitle() {
  try {
    // 获取token（如果需要认证）
    let token = localStorage.getItem("token");
    if (!token) {
      token = "test_token";
      localStorage.setItem("token", token);
    }

    const response = await axios.get(
      `http://127.0.0.1:5000/api/questions/${problemId}?action=get_question`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.status === 200) {
      problemTitle.value = response.data.title || "未知题目";
    }
  } catch (err) {
    console.error("获取题目标题失败:", err);
    problemTitle.value = "未知题目";
  }
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return "未知时间";

  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
}

// 渲染Markdown内容
function renderMarkdown(content) {
  if (!content) return "";
  const rawHtml = marked(content);
  return DOMPurify.sanitize(rawHtml);
}

// 判断内容是否过长需要折叠
function isLongContent(content) {
  // 可以根据字符数或预计渲染高度来判断
  // 这里简单地以500个字符为界限
  return content && content.length > 500;
}

// 切换展开/收起状态
function toggleExpand(solutionId) {
  if (expandedSolutions.value.includes(solutionId)) {
    // 如果已展开，则收起
    expandedSolutions.value = expandedSolutions.value.filter(
      (id) => id !== solutionId
    );
  } else {
    // 如果已收起，则展开
    expandedSolutions.value.push(solutionId);
  }
}

// 评论相关方法
function showComments(solutionId) {
  // 这里可以实现显示评论的逻辑，例如打开评论对话框
  alert("评论功能正在开发中...");
}

// 页面加载时获取题解
onMounted(() => {
  fetchSolutions();
});
</script>

<style scoped>
.solution-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.navigation-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.back-button,
.home-button {
  padding: 8px 16px;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.back-button:hover,
.home-button:hover {
  background-color: #66b1ff;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #409eff;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-message {
  color: #f56c6c;
  text-align: center;
  padding: 20px;
  background-color: #fef0f0;
  border-radius: 4px;
}

.no-solutions {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

.solutions-container h2 {
  margin-bottom: 20px;
  color: #303133;
}

.solution-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.solution-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 20px;
  background-color: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.solution-header {
  margin-bottom: 15px;
}

.solution-title {
  margin: 0 0 10px 0;
  color: #303133;
}

.solution-meta {
  display: flex;
  justify-content: space-between;
  color: #909399;
  font-size: 14px;
}

.solution-content-container {
  position: relative;
  margin-bottom: 20px;
}

.solution-content {
  line-height: 1.6;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.solution-content.collapsed {
  max-height: 300px; /* 设置折叠时的最大高度 */
  position: relative;
}

.solution-content.collapsed::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 80px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 1)
  );
  pointer-events: none;
}

.expand-button {
  display: block;
  margin: 10px auto;
  padding: 6px 12px;
  background-color: #f4f4f5;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: #606266;
  font-size: 14px;
}

.expand-button:hover {
  background-color: #e9e9eb;
}

.solution-actions {
  display: flex;
  gap: 15px;
}

.comment-button {
  padding: 6px 12px;
  background-color: #f4f4f5;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  color: #606266;
}

.comment-button:hover {
  background-color: #e9e9eb;
}

/* 为Markdown内容添加样式 */
.markdown-body {
  color: #303133;
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-body code {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
}

.markdown-body pre {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f6f8fa;
  border-radius: 3px;
}

.markdown-body pre code {
  padding: 0;
  background-color: transparent;
}
</style>

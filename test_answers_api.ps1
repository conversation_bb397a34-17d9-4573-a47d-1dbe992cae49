# 测试题解API
Write-Host "测试题解API..."

# 首先查看数据库中有哪些题目
Write-Host "1. 查看数据库中的题目..."
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/test/db" -Method GET
    Write-Host "✅ 数据库连接正常"
    $data = $response.Content | ConvertFrom-Json
    Write-Host "题目总数: $($data.message)"
    
    if ($data.sample_questions) {
        Write-Host "示例题目:"
        foreach ($q in $data.sample_questions) {
            Write-Host "  ID: $($q.id), 标题: $($q.title)"
        }
        
        # 使用第一个题目ID测试题解API
        $testQuestionId = $data.sample_questions[0].id
        Write-Host ""
        Write-Host "2. 测试题目 $testQuestionId 的题解API..."
        
        try {
            $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/answers/$testQuestionId" -Method GET
            Write-Host "✅ 题解API测试: $($response.StatusCode)"
            
            $answerData = $response.Content | ConvertFrom-Json
            Write-Host "API返回的数据类型: $($answerData.GetType().Name)"
            
            if ($answerData -is [Array]) {
                Write-Host "返回数组，题解数量: $($answerData.Count)"
                if ($answerData.Count -gt 0) {
                    Write-Host "第一个题解:"
                    $firstAnswer = $answerData[0]
                    $firstAnswer.PSObject.Properties | ForEach-Object {
                        Write-Host "  $($_.Name): $($_.Value)"
                    }
                }
            } else {
                Write-Host "返回对象: $($answerData | ConvertTo-Json -Depth 3)"
            }
            
        } catch {
            Write-Host "❌ 题解API测试失败: $($_.Exception.Message)"
            if ($_.Exception.Response) {
                Write-Host "状态码: $($_.Exception.Response.StatusCode)"
                $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
                $responseBody = $reader.ReadToEnd()
                Write-Host "错误详情: $responseBody"
            }
        }
    }
    
} catch {
    Write-Host "❌ 数据库连接测试失败: $($_.Exception.Message)"
}

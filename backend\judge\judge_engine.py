"""
代码判题引擎
支持多种编程语言的代码执行和结果判断
"""

import os
import subprocess
import tempfile
import time
import signal
from typing import Dict, Any, Optional
import json

class JudgeEngine:
    def __init__(self):
        self.supported_languages = {
            'python': {
                'extension': '.py',
                'compile_cmd': None,
                'run_cmd': 'python {filename}',
                'timeout': 5
            },
            'cpp': {
                'extension': '.cpp',
                'compile_cmd': 'g++ -o {output} {filename}',
                'run_cmd': './{output}',
                'timeout': 5
            },
            'java': {
                'extension': '.java',
                'compile_cmd': 'javac {filename}',
                'run_cmd': 'java {classname}',
                'timeout': 5
            },
            'c': {
                'extension': '.c',
                'compile_cmd': 'gcc -o {output} {filename}',
                'run_cmd': './{output}',
                'timeout': 5
            }
        }
    
    def judge_submission(self, code: str, language: str, test_cases: list, 
                        time_limit: int = 5, memory_limit: int = 128) -> Dict[str, Any]:
        """
        判断代码提交的正确性
        
        Args:
            code: 用户提交的代码
            language: 编程语言
            test_cases: 测试用例列表 [{'input': '...', 'expected_output': '...'}, ...]
            time_limit: 时间限制(秒)
            memory_limit: 内存限制(MB)
        
        Returns:
            判题结果字典
        """
        if language not in self.supported_languages:
            return {
                'status': 'Unsupported Language',
                'message': f'不支持的编程语言: {language}',
                'passed_tests': 0,
                'total_tests': len(test_cases),
                'details': []
            }
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            try:
                # 编译代码
                compile_result = self._compile_code(code, language, temp_dir)
                if not compile_result['success']:
                    return {
                        'status': 'Compilation Error',
                        'message': compile_result['error'],
                        'passed_tests': 0,
                        'total_tests': len(test_cases),
                        'details': []
                    }
                
                # 运行测试用例
                results = []
                passed_count = 0
                
                for i, test_case in enumerate(test_cases):
                    result = self._run_test_case(
                        compile_result['executable'], 
                        language, 
                        test_case, 
                        time_limit,
                        temp_dir
                    )
                    results.append(result)
                    if result['status'] == 'Accepted':
                        passed_count += 1
                
                # 确定最终状态
                if passed_count == len(test_cases):
                    final_status = 'Accepted'
                elif passed_count > 0:
                    final_status = 'Partial Accepted'
                else:
                    # 查看是否有其他错误
                    error_types = [r['status'] for r in results if r['status'] != 'Wrong Answer']
                    if error_types:
                        final_status = error_types[0]  # 返回第一个错误类型
                    else:
                        final_status = 'Wrong Answer'
                
                return {
                    'status': final_status,
                    'message': f'通过 {passed_count}/{len(test_cases)} 个测试用例',
                    'passed_tests': passed_count,
                    'total_tests': len(test_cases),
                    'details': results
                }
                
            except Exception as e:
                return {
                    'status': 'System Error',
                    'message': f'系统错误: {str(e)}',
                    'passed_tests': 0,
                    'total_tests': len(test_cases),
                    'details': []
                }
    
    def _compile_code(self, code: str, language: str, temp_dir: str) -> Dict[str, Any]:
        """编译代码"""
        lang_config = self.supported_languages[language]
        
        # 创建源文件
        filename = f"solution{lang_config['extension']}"
        filepath = os.path.join(temp_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(code)
        
        # 如果不需要编译（如Python）
        if lang_config['compile_cmd'] is None:
            return {'success': True, 'executable': filepath}
        
        # 编译
        output_name = "solution"
        if language == 'java':
            # Java特殊处理
            class_name = self._extract_java_class_name(code)
            if class_name:
                output_name = class_name
        
        output_path = os.path.join(temp_dir, output_name)
        compile_cmd = lang_config['compile_cmd'].format(
            filename=filepath, 
            output=output_path,
            classname=output_name
        )
        
        try:
            result = subprocess.run(
                compile_cmd.split(), 
                capture_output=True, 
                text=True, 
                timeout=10,
                cwd=temp_dir
            )
            
            if result.returncode != 0:
                return {
                    'success': False, 
                    'error': result.stderr or result.stdout or '编译失败'
                }
            
            return {'success': True, 'executable': output_path}
            
        except subprocess.TimeoutExpired:
            return {'success': False, 'error': '编译超时'}
        except Exception as e:
            return {'success': False, 'error': f'编译错误: {str(e)}'}
    
    def _run_test_case(self, executable: str, language: str, test_case: Dict[str, str], 
                      time_limit: int, temp_dir: str) -> Dict[str, Any]:
        """运行单个测试用例"""
        lang_config = self.supported_languages[language]
        
        # 准备运行命令
        if language == 'python':
            run_cmd = lang_config['run_cmd'].format(filename=executable)
        elif language == 'java':
            class_name = os.path.basename(executable)
            run_cmd = lang_config['run_cmd'].format(classname=class_name)
        else:
            run_cmd = lang_config['run_cmd'].format(output=executable)
        
        try:
            # 运行程序
            process = subprocess.Popen(
                run_cmd.split(),
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=temp_dir
            )
            
            # 设置超时
            try:
                stdout, stderr = process.communicate(
                    input=test_case['input'], 
                    timeout=time_limit
                )
            except subprocess.TimeoutExpired:
                process.kill()
                return {
                    'status': 'Time Limit Exceeded',
                    'input': test_case['input'],
                    'expected': test_case['expected_output'],
                    'actual': '',
                    'error': '运行超时'
                }
            
            if process.returncode != 0:
                return {
                    'status': 'Runtime Error',
                    'input': test_case['input'],
                    'expected': test_case['expected_output'],
                    'actual': '',
                    'error': stderr or '运行时错误'
                }
            
            # 比较输出
            actual_output = stdout.strip()
            expected_output = test_case['expected_output'].strip()
            
            if actual_output == expected_output:
                return {
                    'status': 'Accepted',
                    'input': test_case['input'],
                    'expected': expected_output,
                    'actual': actual_output,
                    'error': ''
                }
            else:
                return {
                    'status': 'Wrong Answer',
                    'input': test_case['input'],
                    'expected': expected_output,
                    'actual': actual_output,
                    'error': '输出不匹配'
                }
                
        except Exception as e:
            return {
                'status': 'System Error',
                'input': test_case['input'],
                'expected': test_case['expected_output'],
                'actual': '',
                'error': f'系统错误: {str(e)}'
            }
    
    def _extract_java_class_name(self, code: str) -> Optional[str]:
        """从Java代码中提取类名"""
        import re
        match = re.search(r'public\s+class\s+(\w+)', code)
        return match.group(1) if match else 'Solution'

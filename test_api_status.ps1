# 测试API状态
Write-Host "测试API连接状态..."

# 1. 测试基本连接
Write-Host "1. 测试基本API连接..."
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/test" -Method GET
    Write-Host "✅ 基本连接测试: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "❌ 基本连接测试失败: $($_.Exception.Message)"
}

Write-Host ""

# 2. 测试数据库连接
Write-Host "2. 测试数据库连接..."
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/test/db" -Method GET
    Write-Host "✅ 数据库连接测试: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "❌ 数据库连接测试失败: $($_.Exception.Message)"
}

Write-Host ""

# 3. 测试题解API（无认证）
Write-Host "3. 测试题解API（无认证）..."
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/solutions?pg=1&ppg=10" -Method GET
    Write-Host "✅ 题解API测试: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "❌ 题解API测试失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)"
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

Write-Host ""

# 4. 测试题解API（有认证）
Write-Host "4. 测试题解API（有认证）..."
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/solutions?pg=1&ppg=10" -Method GET -Headers @{'Authorization'='Bearer test_token'}
    Write-Host "✅ 题解API认证测试: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "❌ 题解API认证测试失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)"
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

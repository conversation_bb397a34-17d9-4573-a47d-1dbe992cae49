-- 创建数据库（如果不存在）
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'OnlineJudge')
BEGIN
    CREATE DATABASE OnlineJudge;
END
GO

USE OnlineJudge;
GO

-- 创建 Users 表
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Users')
BEGIN
    CREATE TABLE Users (
        UserID INT PRIMARY KEY IDENTITY(1,1),
        UserName NVARCHAR(100) NOT NULL,
        Password NVARCHAR(255) NOT NULL,
        Status NVARCHAR(20) DEFAULT 'active',
        Level NVARCHAR(20) NOT NULL DEFAULT 'user',
        UserPict NVARCHAR(MAX),
        RegistrationTime DATETIME DEFAULT GETUTCDATE()
    );
END
GO

-- 创建 Questions 表
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Questions')
BEGIN
    CREATE TABLE Questions (
        QuesID INT PRIMARY KEY IDENTITY(1,1),
        QuesType NVARCHAR(20) NOT NULL,
        QuesLevel NVARCHAR(20) NOT NULL,
        QuesName NVARCHAR(100) NOT NULL,
        QuesDescrip NVARCHAR(1024) NOT NULL,
        IptFomart NVARCHAR(100) NOT NULL,
        OptFomart NVARCHAR(100) NOT NULL,
        IptExample NVARCHAR(MAX),
        OptExample NVARCHAR(MAX),
        SubNum INT DEFAULT 0,
        CollNum INT DEFAULT 0,
        PassNum INT DEFAULT 0
    );
END
GO

-- 创建 Answers 表
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Answers')
BEGIN
    CREATE TABLE Answers (
        AnsID INT PRIMARY KEY IDENTITY(1,1),
        QuesID INT NOT NULL,
        AnsTitle NVARCHAR(MAX),
        Content NVARCHAR(MAX),
        AnsStatus NVARCHAR(20) NOT NULL DEFAULT 'pending',
        TIME DATETIME DEFAULT GETUTCDATE(),
        UserID INT NOT NULL,
        FOREIGN KEY (QuesID) REFERENCES Questions(QuesID),
        FOREIGN KEY (UserID) REFERENCES Users(UserID)
    );
END
GO

-- 创建 Collections 表
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Collections')
BEGIN
    CREATE TABLE Collections (
        QuesID INT NOT NULL,
        UserID INT NOT NULL,
        CollTime DATETIME DEFAULT GETUTCDATE(),
        QuesRemark NVARCHAR(100),
        PRIMARY KEY (QuesID, UserID),
        FOREIGN KEY (QuesID) REFERENCES Questions(QuesID),
        FOREIGN KEY (UserID) REFERENCES Users(UserID)
    );
END
GO

-- 创建 Submissions 表
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Submissions')
BEGIN
    CREATE TABLE Submissions (
        SubID INT PRIMARY KEY IDENTITY(1,1),
        UserID INT NOT NULL,
        QuesID INT NOT NULL,
        SubContent NVARCHAR(MAX),
        SubTime DATETIME DEFAULT GETUTCDATE(),
        PassStatus NVARCHAR(20) NOT NULL,
        FOREIGN KEY (UserID) REFERENCES Users(UserID),
        FOREIGN KEY (QuesID) REFERENCES Questions(QuesID)
    );
END
GO

-- 创建 Reviews 表
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Reviews')
BEGIN
    CREATE TABLE Reviews (
        ReviID INT PRIMARY KEY IDENTITY(1,1),
        UserID INT NOT NULL,
        QuesID INT,
        ParentReviID INT,
        ReviCont NVARCHAR(MAX),
        ReviDate DATETIME DEFAULT GETUTCDATE(),
        FOREIGN KEY (UserID) REFERENCES Users(UserID),
        FOREIGN KEY (QuesID) REFERENCES Questions(QuesID),
        FOREIGN KEY (ParentReviID) REFERENCES Reviews(ReviID)
    );
END
GO

-- 创建 ReviewsFeedback 表
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'ReviewsFeedback')
BEGIN
    CREATE TABLE ReviewsFeedback (
        ReviFbID INT PRIMARY KEY IDENTITY(1,1),
        ReviID INT NOT NULL,
        FbTime DATETIME DEFAULT GETUTCDATE(),
        FbCont NVARCHAR(MAX),
        UserID INT NOT NULL,
        FOREIGN KEY (ReviID) REFERENCES Reviews(ReviID),
        FOREIGN KEY (UserID) REFERENCES Users(UserID)
    );
END
GO

-- 创建 QuestionsFeedback 表
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'QuestionsFeedback')
BEGIN
    CREATE TABLE QuestionsFeedback (
        QuesFbID INT PRIMARY KEY IDENTITY(1,1),
        QuesID INT NOT NULL,
        FbTime DATETIME DEFAULT GETUTCDATE(),
        FbCont NVARCHAR(MAX),
        UserID INT NOT NULL,
        FOREIGN KEY (QuesID) REFERENCES Questions(QuesID),
        FOREIGN KEY (UserID) REFERENCES Users(UserID)
    );
END
GO

-- 创建管理员账户
IF NOT EXISTS (SELECT * FROM Users WHERE UserName = 'admin')
BEGIN
    -- 密码为 'admin'，这里使用明文（实际应用中应该使用哈希值）
    INSERT INTO Users (UserName, Password, Status, Level)
    VALUES ('admin', 'admin', 'active', 'admin');
END
GO

-- 创建普通用户账户
IF NOT EXISTS (SELECT * FROM Users WHERE UserName = 'user')
BEGIN
    -- 密码为 'user'，这里使用明文（实际应用中应该使用哈希值）
    INSERT INTO Users (UserName, Password, Status, Level)
    VALUES ('user', 'user', 'active', 'user');
END
GO

PRINT 'All tables created successfully!';
USE OnlineJudge;
GO

-- 清空现有数据（可选，谨慎使用）
-- DELETE FROM QuestionsFeedback;
-- DELETE FROM ReviewsFeedback;
-- DELETE FROM Reviews;
-- DELETE FROM Submissions;
-- DELETE FROM Collections;
-- DELETE FROM Answers;
-- DELETE FROM Questions;
-- DELETE FROM Users WHERE UserName NOT IN ('admin', 'user');

-- 插入更多用户
INSERT INTO Users (UserName, Password, Status, Level, RegistrationTime)
VALUES 
('student1', 'password123', 'active', 'user', GETUTCDATE()),
('teacher1', 'secure456', 'active', 'user', GETUTCDATE()),
('moderator', 'mod789', 'active', 'admin', GETUTCDATE()),
('guest', 'guest123', 'active', 'guest', GETUTCDATE());
GO

-- 获取用户ID
DECLARE @admin_id INT;
DECLARE @user_id INT;
DECLARE @student1_id INT;
DECLARE @teacher1_id INT;
DECLARE @moderator_id INT;

SELECT @admin_id = UserID FROM Users WHERE UserName = 'admin';
SELECT @user_id = UserID FROM Users WHERE UserName = 'user';
SELECT @student1_id = UserID FROM Users WHERE UserName = 'student1';
SELECT @teacher1_id = UserID FROM Users WHERE UserName = 'teacher1';
SELECT @moderator_id = UserID FROM Users WHERE UserName = 'moderator';

-- 检查用户ID是否存在
IF @admin_id IS NULL OR @user_id IS NULL OR @student1_id IS NULL OR @teacher1_id IS NULL
BEGIN
    PRINT N'错误：一个或多个用户ID不存在，请确保admin和user用户已创建';
    RETURN;
END

-- 插入编程题目
INSERT INTO Questions (QuesType, QuesLevel, QuesName, QuesDescrip, IptFomart, OptFomart, IptExample, OptExample)
VALUES
-- 简单题目
(N'算法', N'简单', N'两数之和', N'给定一个整数数组 nums 和一个目标值 target，请你在该数组中找出和为目标值的那两个整数，并返回他们的数组下标。', 
N'第一行输入数组，第二行输入目标值', N'返回两个整数的下标', N'[2,7,11,15]\n9', N'[0,1]'),

(N'数据结构', N'简单', N'有效的括号', N'给定一个只包括 ''(''，'')''，''{''，''}''，''[''，'']'' 的字符串，判断字符串是否有效。', 
N'输入一个字符串', N'如果字符串有效返回 true，否则返回 false', N'()[]{}', N'true'),

(N'字符串', N'简单', N'回文字符串', N'给定一个字符串，验证它是否是回文串，只考虑字母和数字字符，可以忽略字母的大小写。', 
N'输入一个字符串', N'如果是回文串返回 true，否则返回 false', N'A man, a plan, a canal: Panama', N'true'),

-- 中等题目
(N'算法', N'中等', N'三数之和', N'给你一个包含 n 个整数的数组 nums，判断 nums 中是否存在三个元素 a，b，c ，使得 a + b + c = 0 ？请你找出所有满足条件且不重复的三元组。', 
N'输入一个整数数组', N'输出所有不重复的三元组', N'[-1,0,1,2,-1,-4]', N'[[-1,-1,2],[-1,0,1]]'),

(N'数据结构', N'中等', N'二叉树的层序遍历', N'给你一个二叉树，请你返回其按层序遍历得到的节点值。', 
N'输入一个二叉树', N'返回层序遍历结果', N'[3,9,20,null,null,15,7]', N'[[3],[9,20],[15,7]]'),

(N'动态规划', N'中等', N'最长递增子序列', N'给定一个无序的整数数组，找到其中最长上升子序列的长度。', 
N'输入一个整数数组', N'返回最长上升子序列的长度', N'[10,9,2,5,3,7,101,18]', N'4'),

-- 困难题目
(N'算法', N'困难', N'最短回文串', N'给定一个字符串 s，你可以通过在字符串前面添加字符将其转换为回文串。找到并返回可以用这种方式转换的最短回文串。', 
N'输入一个字符串', N'返回添加字符后的最短回文串', N'aacecaaa', N'aaacecaaa'),

(N'图论', N'困难', N'课程表 III', N'这里有 n 门不同的在线课程，按从 1 到 n 编号。给你一个数组 courses ，其中 courses[i] = [durationi, lastDayi] 表示第 i 门课将会持续上 durationi 天课，并且必须在不晚于 lastDayi 的时候完成。你的学期从第 1 天开始。且不能同时修读两门及两门以上的课程。返回你最多可以修读的课程数目。', 
N'输入课程数组，每个课程包含持续时间和截止日期', N'返回最多可以修读的课程数目', N'[[100,200],[200,1300],[1000,1250],[2000,3200]]', N'3'),

(N'动态规划', N'困难', N'正则表达式匹配', N'给你一个字符串 s 和一个字符规律 p，请你来实现一个支持 ''.'' 和 ''*'' 的正则表达式匹配。', 
N'第一行输入字符串s，第二行输入模式p', N'如果匹配成功返回true，否则返回false', N'aa\na*', N'true');
GO

-- 验证题目是否已插入并获取题目ID
DECLARE @ques1_id INT, @ques2_id INT, @ques3_id INT, @ques4_id INT;
DECLARE @ques5_id INT, @ques6_id INT, @ques7_id INT, @ques8_id INT, @ques9_id INT;

SELECT @ques1_id = QuesID FROM Questions WHERE QuesName = N'两数之和';
SELECT @ques2_id = QuesID FROM Questions WHERE QuesName = N'有效的括号';
SELECT @ques3_id = QuesID FROM Questions WHERE QuesName = N'回文字符串';
SELECT @ques4_id = QuesID FROM Questions WHERE QuesName = N'三数之和';
SELECT @ques5_id = QuesID FROM Questions WHERE QuesName = N'二叉树的层序遍历';
SELECT @ques6_id = QuesID FROM Questions WHERE QuesName = N'最长递增子序列';
SELECT @ques7_id = QuesID FROM Questions WHERE QuesName = N'最短回文串';
SELECT @ques8_id = QuesID FROM Questions WHERE QuesName = N'课程表 III';
SELECT @ques9_id = QuesID FROM Questions WHERE QuesName = N'正则表达式匹配';

-- 检查题目ID是否存在
IF @ques1_id IS NULL OR @ques2_id IS NULL OR @ques3_id IS NULL OR @ques4_id IS NULL OR
   @ques5_id IS NULL OR @ques6_id IS NULL OR @ques7_id IS NULL OR @ques8_id IS NULL OR @ques9_id IS NULL
BEGIN
    PRINT N'错误：一个或多个题目ID不存在，请检查题目插入是否成功';
    RETURN;
END

-- 重新获取用户ID
DECLARE @admin_id INT;
DECLARE @user_id INT;
DECLARE @student1_id INT;
DECLARE @teacher1_id INT;

SELECT @admin_id = UserID FROM Users WHERE UserName = 'admin';
SELECT @user_id = UserID FROM Users WHERE UserName = 'user';
SELECT @student1_id = UserID FROM Users WHERE UserName = 'student1';
SELECT @teacher1_id = UserID FROM Users WHERE UserName = 'teacher1';

-- 插入题解
INSERT INTO Answers (QuesID, AnsTitle, Content, AnsStatus, TIME, UserID)
VALUES
(@ques1_id, N'两数之和的最优解法', N'使用哈希表可以将时间复杂度降低到O(n)。遍历数组时，检查target-当前值是否在哈希表中，如果不在则将当前值加入哈希表。', N'approved', GETUTCDATE(), @admin_id),
(@ques2_id, N'栈解决括号匹配问题', N'使用栈结构可以轻松解决括号匹配问题。遇到左括号入栈，遇到右括号时检查栈顶是否匹配，不匹配则无效。', N'approved', GETUTCDATE(), @user_id),
(@ques4_id, N'三数之和的排序+双指针解法', N'首先对数组排序，然后固定一个数，使用双指针寻找剩余两个数。注意去重处理。', N'pending', GETUTCDATE(), @student1_id),
(@ques6_id, N'动态规划解决LIS问题', N'dp[i]表示以第i个数结尾的最长上升子序列长度。对每个j<i，如果nums[j]<nums[i]，则可以更新dp[i]=max(dp[i],dp[j]+1)。', N'approved', GETUTCDATE(), @admin_id);
GO

-- 重新获取用户ID和题目ID
DECLARE @admin_id INT, @user_id INT, @student1_id INT, @teacher1_id INT;
DECLARE @ques1_id INT, @ques2_id INT, @ques3_id INT, @ques4_id INT, @ques6_id INT;

SELECT @admin_id = UserID FROM Users WHERE UserName = 'admin';
SELECT @user_id = UserID FROM Users WHERE UserName = 'user';
SELECT @student1_id = UserID FROM Users WHERE UserName = 'student1';
SELECT @teacher1_id = UserID FROM Users WHERE UserName = 'teacher1';

SELECT @ques1_id = QuesID FROM Questions WHERE QuesName = N'两数之和';
SELECT @ques2_id = QuesID FROM Questions WHERE QuesName = N'有效的括号';
SELECT @ques3_id = QuesID FROM Questions WHERE QuesName = N'回文字符串';
SELECT @ques4_id = QuesID FROM Questions WHERE QuesName = N'三数之和';
SELECT @ques6_id = QuesID FROM Questions WHERE QuesName = N'最长递增子序列';

-- 插入提交记录
INSERT INTO Submissions (UserID, QuesID, SubContent, SubTime, PassStatus)
VALUES
(@user_id, @ques1_id, N'# Python代码：两数之和哈希表解法', GETUTCDATE(), N'passed'),
(@student1_id, @ques1_id, N'// JavaScript代码：两数之和Map解法', GETUTCDATE(), N'passed'),
(@user_id, @ques2_id, N'# Python代码：有效括号栈解法', GETUTCDATE(), N'passed'),
(@teacher1_id, @ques3_id, N'// JavaScript代码：回文字符串解法', GETUTCDATE(), N'failed'),
(@student1_id, @ques4_id, N'/* Java代码：三数之和排序+双指针解法 */', GETUTCDATE(), N'passed'),
(@user_id, @ques6_id, N'# Python代码：最长递增子序列动态规划解法', GETUTCDATE(), N'passed');
GO

-- 重新获取用户ID和题目ID
DECLARE @admin_id INT, @user_id INT, @student1_id INT, @teacher1_id INT;
DECLARE @ques1_id INT, @ques2_id INT, @ques4_id INT, @ques6_id INT, @ques7_id INT;

SELECT @admin_id = UserID FROM Users WHERE UserName = 'admin';
SELECT @user_id = UserID FROM Users WHERE UserName = 'user';
SELECT @student1_id = UserID FROM Users WHERE UserName = 'student1';
SELECT @teacher1_id = UserID FROM Users WHERE UserName = 'teacher1';

SELECT @ques1_id = QuesID FROM Questions WHERE QuesName = N'两数之和';
SELECT @ques2_id = QuesID FROM Questions WHERE QuesName = N'有效的括号';
SELECT @ques4_id = QuesID FROM Questions WHERE QuesName = N'三数之和';
SELECT @ques6_id = QuesID FROM Questions WHERE QuesName = N'最长递增子序列';
SELECT @ques7_id = QuesID FROM Questions WHERE QuesName = N'最短回文串';

-- 插入收藏记录
INSERT INTO Collections (QuesID, UserID, CollTime, QuesRemark)
VALUES
(@ques1_id, @user_id, GETUTCDATE(), N'经典入门题'),
(@ques4_id, @user_id, GETUTCDATE(), N'面试常考题'),
(@ques6_id, @student1_id, GETUTCDATE(), N'重要的动态规划题目'),
(@ques7_id, @student1_id, GETUTCDATE(), N'需要复习的困难题'),
(@ques2_id, @teacher1_id, GETUTCDATE(), N'基础数据结构题');
GO

-- 重新获取用户ID和题目ID
DECLARE @admin_id INT, @user_id INT, @student1_id INT, @teacher1_id INT;
DECLARE @ques1_id INT, @ques4_id INT, @ques6_id INT, @ques7_id INT;

SELECT @admin_id = UserID FROM Users WHERE UserName = 'admin';
SELECT @user_id = UserID FROM Users WHERE UserName = 'user';
SELECT @student1_id = UserID FROM Users WHERE UserName = 'student1';
SELECT @teacher1_id = UserID FROM Users WHERE UserName = 'teacher1';

SELECT @ques1_id = QuesID FROM Questions WHERE QuesName = N'两数之和';
SELECT @ques4_id = QuesID FROM Questions WHERE QuesName = N'三数之和';
SELECT @ques6_id = QuesID FROM Questions WHERE QuesName = N'最长递增子序列';
SELECT @ques7_id = QuesID FROM Questions WHERE QuesName = N'最短回文串';

-- 插入评论
INSERT INTO Reviews (UserID, QuesID, ParentReviID, ReviCont, ReviDate)
VALUES
(@user_id, @ques1_id, NULL, N'这道题是算法入门的经典题目，建议新手先掌握暴力解法，再学习哈希表优化。', GETUTCDATE());
GO

-- 重新获取用户ID和题目ID
DECLARE @admin_id INT, @user_id INT, @student1_id INT, @teacher1_id INT;
DECLARE @ques1_id INT, @ques4_id INT, @ques6_id INT, @ques7_id INT;

SELECT @admin_id = UserID FROM Users WHERE UserName = 'admin';
SELECT @user_id = UserID FROM Users WHERE UserName = 'user';
SELECT @student1_id = UserID FROM Users WHERE UserName = 'student1';
SELECT @teacher1_id = UserID FROM Users WHERE UserName = 'teacher1';

SELECT @ques1_id = QuesID FROM Questions WHERE QuesName = N'两数之和';
SELECT @ques4_id = QuesID FROM Questions WHERE QuesName = N'三数之和';
SELECT @ques6_id = QuesID FROM Questions WHERE QuesName = N'最长递增子序列';
SELECT @ques7_id = QuesID FROM Questions WHERE QuesName = N'最短回文串';

-- 获取第一条评论的ID
DECLARE @first_review_id INT;
SELECT TOP 1 @first_review_id = ReviID FROM Reviews WHERE UserID = @user_id AND QuesID = @ques1_id AND ParentReviID IS NULL;

-- 继续插入评论，使用获取到的评论ID作为父评论
INSERT INTO Reviews (UserID, QuesID, ParentReviID, ReviCont, ReviDate)
VALUES
(@student1_id, @ques1_id, @first_review_id, N'同意，而且这题在实际工作中也经常用到类似的思路。', GETUTCDATE()),
(@user_id, @ques4_id, NULL, N'这道题的难点在于如何去除重复解，排序是一个很好的预处理步骤。', GETUTCDATE()),
(@teacher1_id, @ques6_id, NULL, N'动态规划的思路需要多练习才能掌握，这是一道很好的入门题。', GETUTCDATE()),
(@admin_id, @ques7_id, NULL, N'这道困难题需要结合KMP算法才能高效解决。', GETUTCDATE());
GO

-- 重新获取用户ID
DECLARE @admin_id INT, @user_id INT, @student1_id INT, @teacher1_id INT;

SELECT @admin_id = UserID FROM Users WHERE UserName = 'admin';
SELECT @user_id = UserID FROM Users WHERE UserName = 'user';
SELECT @student1_id = UserID FROM Users WHERE UserName = 'student1';
SELECT @teacher1_id = UserID FROM Users WHERE UserName = 'teacher1';

-- 获取评论ID
DECLARE @review1_id INT;
DECLARE @review3_id INT;
DECLARE @review5_id INT;

SELECT TOP 1 @review1_id = ReviID FROM Reviews WHERE UserID = @user_id AND QuesID = (SELECT QuesID FROM Questions WHERE QuesName = N'两数之和') AND ParentReviID IS NULL;
SELECT TOP 1 @review3_id = ReviID FROM Reviews WHERE UserID = @user_id AND QuesID = (SELECT QuesID FROM Questions WHERE QuesName = N'三数之和');
SELECT TOP 1 @review5_id = ReviID FROM Reviews WHERE UserID = @admin_id AND QuesID = (SELECT QuesID FROM Questions WHERE QuesName = N'最短回文串');

-- 检查评论ID是否存在
IF @review1_id IS NULL OR @review3_id IS NULL OR @review5_id IS NULL
BEGIN
    PRINT N'错误：一个或多个评论ID不存在，请检查评论插入是否成功';
    RETURN;
END

-- 插入评论反馈
INSERT INTO ReviewsFeedback (ReviID, FbTime, FbCont, UserID)
VALUES
(@review1_id, GETUTCDATE(), N'这个评论很有帮助，谢谢分享！', @teacher1_id),
(@review3_id, GETUTCDATE(), N'请问能详细解释一下去重的具体步骤吗？', @user_id),
(@review5_id, GETUTCDATE(), N'能推荐一些KMP算法的学习资料吗？', @student1_id);
GO

-- 重新获取用户ID和题目ID
DECLARE @admin_id INT, @user_id INT, @student1_id INT;
DECLARE @ques2_id INT, @ques5_id INT, @ques8_id INT;

SELECT @admin_id = UserID FROM Users WHERE UserName = 'admin';
SELECT @user_id = UserID FROM Users WHERE UserName = 'user';
SELECT @student1_id = UserID FROM Users WHERE UserName = 'student1';

SELECT @ques2_id = QuesID FROM Questions WHERE QuesName = N'有效的括号';
SELECT @ques5_id = QuesID FROM Questions WHERE QuesName = N'二叉树的层序遍历';
SELECT @ques8_id = QuesID FROM Questions WHERE QuesName = N'课程表 III';

-- 插入题目反馈
INSERT INTO QuestionsFeedback (QuesID, FbTime, FbCont, UserID)
VALUES
(@ques2_id, GETUTCDATE(), N'题目描述不够清晰，建议添加更多示例。', @student1_id),
(@ques5_id, GETUTCDATE(), N'输入格式说明不够详细，不清楚如何表示二叉树。', @user_id),
(@ques8_id, GETUTCDATE(), N'这道题难度可能被低估了，建议调整为特难级别。', @admin_id);
GO

-- 更新题目的提交数和通过数
UPDATE Questions
SET SubNum = (SELECT COUNT(*) FROM Submissions WHERE Submissions.QuesID = Questions.QuesID),
    PassNum = (SELECT COUNT(*) FROM Submissions WHERE Submissions.QuesID = Questions.QuesID AND Submissions.PassStatus = 'passed'),
    CollNum = (SELECT COUNT(*) FROM Collections WHERE Collections.QuesID = Questions.QuesID);
GO

PRINT N'示例数据已成功插入!';




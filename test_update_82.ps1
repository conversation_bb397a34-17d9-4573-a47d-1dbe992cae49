# 测试更新题目ID 82
Write-Host "测试更新题目ID 82..."

$updateBody = @{
    id = 82  # 使用刚创建的题目ID
    title = "更新后的测试题目"
    difficulty = "medium"
    description = "这是更新后的题目描述"
    tags = @("算法", "进阶")
    inputDescription = "更新后的输入描述"
    outputDescription = "更新后的输出描述"
    examples = @(
        @{
            input = "2 3"
            output = "5"
        }
    )
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/admin/questions?action=update_question" -Method POST -Headers @{'Authorization'='Bearer test_token'; 'Content-Type'='application/json'} -Body $updateBody
    Write-Host "✅ 更新题目测试: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "❌ 更新题目测试失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

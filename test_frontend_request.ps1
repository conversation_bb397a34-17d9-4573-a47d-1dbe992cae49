# 测试前端请求格式
Write-Host "测试前端请求格式..."

# 模拟前端发送的请求
$frontendRequest = @{
    params = @{
        page = 1
        per_page = 10
    }
} | ConvertTo-Json -Depth 3

Write-Host "前端请求格式:"
Write-Host $frontendRequest

Write-Host ""
Write-Host "发送请求到后端..."

try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/questions/filter" -Method POST -Body $frontendRequest -ContentType "application/json"
    Write-Host "✅ 请求成功: $($response.StatusCode)"
    $data = $response.Content | ConvertFrom-Json
    Write-Host "题目数量: $($data.problems.Count)"
    Write-Host "总数: $($data.total)"
    if ($data.problems.Count -gt 0) {
        Write-Host "第一个题目: $($data.problems[0].title)"
    }
} catch {
    Write-Host "❌ 请求失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

<template>
  <div class="problem-list-page">
    <h1>题目列表</h1>

    <!-- 调试信息 -->
    <div class="debug-info">
      <p>加载状态: {{ loading ? "加载中" : "已加载" }}</p>
      <p>题目数量: {{ problemList.length }}</p>
      <p>错误信息: {{ error }}</p>
    </div>

    <!-- 过滤器 -->
    <div class="filter-section">
      <!-- 过滤器组件 -->
    </div>

    <!-- 题目表格 -->
    <div class="problem-list-container">
      <table v-if="problemList.length > 0">
        <thead>
          <tr>
            <th>ID</th>
            <th>题目</th>
            <th>难度</th>
            <th>标签</th>
            <th>通过/提交</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="problem in problemList"
            :key="problem.id"
            @click="navigateToProblem(problem.id)"
          >
            <td>{{ problem.id }}</td>
            <td>{{ problem.title }}</td>
            <td>{{ problem.difficulty }}</td>
            <td>{{ problem.tags }}</td>
            <td>{{ problem.accepted }}/{{ problem.submitted }}</td>
          </tr>
        </tbody>
      </table>

      <div v-else-if="!loading" class="no-data">暂无题目数据</div>

      <div v-if="loading" class="loading">加载中...</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import axios from "axios";

const router = useRouter();
const problemList = ref([]);
const loading = ref(false);
const error = ref("");

// 获取题目列表
async function fetchProblems() {
  loading.value = true;
  error.value = "";

  try {
    console.log("正在获取题目列表...");

    // 直接使用 axios 发送请求
    const response = await axios.post(
      "http://127.0.0.1:5000/api/questions/filter",
      {
        page: 1,
        per_page: 15,
      }
    );

    console.log("API响应:", response.data);

    if (response.data.problems && Array.isArray(response.data.problems)) {
      problemList.value = response.data.problems;
    } else {
      // 使用测试数据
      problemList.value = [
        {
          id: 1,
          title: "两数之和 (测试数据)",
          difficulty: "简单",
          tags: "数组",
          accepted: 5,
          submitted: 10,
        },
        {
          id: 2,
          title: "有效的括号 (测试数据)",
          difficulty: "中等",
          tags: "栈",
          accepted: 3,
          submitted: 7,
        },
      ];

      error.value = "服务器返回的数据格式不正确，显示测试数据";
    }
  } catch (err) {
    console.error("获取题目失败", err);
    error.value = `获取题目失败: ${err.message}`;

    // 使用测试数据
    problemList.value = [
      {
        id: 1,
        title: "两数之和 (测试数据)",
        difficulty: "简单",
        tags: "数组",
        accepted: 5,
        submitted: 10,
      },
      {
        id: 2,
        title: "有效的括号 (测试数据)",
        difficulty: "中等",
        tags: "栈",
        accepted: 3,
        submitted: 7,
      },
    ];
  } finally {
    loading.value = false;
  }
}

function navigateToProblem(id) {
  router.push(`/problem/${id}`);
}

// 初始加载
onMounted(() => {
  fetchProblems();
});
</script>

<style scoped>
.problem-list-page {
  padding: 20px;
}

.debug-info {
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  padding: 10px;
  margin-bottom: 20px;
  font-family: monospace;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th,
td {
  padding: 10px;
  border: 1px solid #ddd;
  text-align: left;
}

tr:hover {
  background-color: #f5f5f5;
  cursor: pointer;
}

.loading,
.no-data {
  text-align: center;
  padding: 20px;
  color: #666;
}
</style>

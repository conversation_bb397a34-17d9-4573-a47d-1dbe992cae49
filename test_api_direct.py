import sys
sys.path.append('backend')

from flask import Flask
from models import *
from database import db
from questions import get_selected_questions
import json

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = (
    'mssql+pyodbc://localhost/OnlineJudge?trusted_connection=yes&driver=ODBC+Driver+17+for+SQL+Server&trusted_connection=yes&TrustServerCertificate=yes')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

app.config['DB_CONNECTION_POOLS'] = {
    'default': {
        'host': 'localhost',
        'port': 1433,
        'user': 'admin',
        'password': 'admin',
        'database': 'OnlineJudge',
        'uri': 'mssql+pyodbc://localhost/OnlineJudge?trusted_connection=yes&driver=ODBC+Driver+17+for+SQL+Server&trusted_connection=yes&TrustServerCertificate=yes',
        'pool_size': 5,
        'max_overflow': 10
    }
}

db.init_app(app)

def test_api_function():
    """直接测试API函数"""
    with app.app_context():
        print("=== 直接测试API函数 ===")
        
        # 模拟API请求数据
        filter_data = {
            "page": 1,
            "per_page": 15
        }
        
        print(f"请求数据: {filter_data}")
        
        # 调用API函数
        response = get_selected_questions(filter_data)
        
        print(f"API响应类型: {type(response)}")
        print(f"API响应: {response}")
        
        # 如果是Flask Response对象，获取数据
        if hasattr(response, 'get_data'):
            data = response.get_data(as_text=True)
            print(f"响应数据: {data}")
            
            # 解析JSON
            try:
                json_data = json.loads(data)
                print(f"解析后的JSON: {json_data}")
                print(f"题目数量: {len(json_data.get('problems', []))}")
                print(f"总数: {json_data.get('total', 0)}")
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")

if __name__ == "__main__":
    test_api_function()

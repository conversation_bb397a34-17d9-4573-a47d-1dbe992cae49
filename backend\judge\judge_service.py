"""
判题服务
处理判题请求，管理判题队列
"""

import threading
import queue
import time
from typing import Dict, Any, List
from .judge_engine import JudgeEngine
from models import Submissions, Questions
from database import db

class JudgeService:
    def __init__(self):
        self.judge_engine = JudgeEngine()
        self.judge_queue = queue.Queue()
        self.is_running = False
        self.worker_thread = None

    def start(self):
        """启动判题服务"""
        if not self.is_running:
            self.is_running = True
            self.worker_thread = threading.Thread(target=self._worker, daemon=True)
            self.worker_thread.start()

    def stop(self):
        """停止判题服务"""
        self.is_running = False
        if self.worker_thread:
            self.worker_thread.join()

    def submit_for_judge(self, submission_id: int):
        """提交代码到判题队列"""
        self.judge_queue.put(submission_id)

    def _worker(self):
        """判题工作线程"""
        while self.is_running:
            try:
                # 从队列获取提交ID，超时1秒
                submission_id = self.judge_queue.get(timeout=1)
                self._judge_submission(submission_id)
                self.judge_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                print(f"判题工作线程错误: {e}")

    def _judge_submission(self, submission_id: int):
        """判题单个提交"""
        try:
            # 获取提交信息
            submission = Submissions.query.get(submission_id)
            if not submission:
                return

            # 获取题目信息
            question = Questions.query.get(submission.QuesID)
            if not question:
                self._update_submission_status(submission_id, 'System Error', '题目不存在')
                return

            # 获取测试用例
            test_cases = self._get_test_cases(question.QuesID)
            if not test_cases:
                # 如果没有测试用例，使用题目的示例
                test_cases = [{
                    'input': question.IptExample or '',
                    'expected_output': question.OptExample or ''
                }]

            # 执行判题
            result = self.judge_engine.judge_submission(
                code=submission.SubContent,
                language=submission.Language or 'python',  # 默认Python
                test_cases=test_cases,
                time_limit=5,
                memory_limit=128
            )

            # 更新提交状态
            self._update_submission_status(
                submission_id,
                result['status'],
                result['message'],
                result.get('details', [])
            )

            # 更新题目统计
            self._update_question_stats(question.QuesID, result['status'])

        except Exception as e:
            self._update_submission_status(submission_id, 'System Error', f'判题系统错误: {str(e)}')

    def _get_test_cases(self, question_id: int) -> List[Dict[str, str]]:
        """获取题目的测试用例"""
        from .test_cases import get_test_cases
        return get_test_cases(question_id)

    def _update_submission_status(self, submission_id: int, status: str, message: str, details: List = None):
        """更新提交状态"""
        try:
            submission = Submissions.query.get(submission_id)
            if submission:
                submission.PassStatus = status
                submission.JudgeResult = message
                if details:
                    submission.JudgeDetails = str(details)  # 存储详细结果
                db.session.commit()
        except Exception as e:
            db.session.rollback()
            print(f"更新提交状态失败: {e}")

    def _update_question_stats(self, question_id: int, status: str):
        """更新题目统计信息"""
        try:
            question = Questions.query.get(question_id)
            if question:
                if status == 'Accepted':
                    question.PassNum = (question.PassNum or 0) + 1
                question.SubNum = (question.SubNum or 0) + 1
                db.session.commit()
        except Exception as e:
            db.session.rollback()
            print(f"更新题目统计失败: {e}")

# 全局判题服务实例
judge_service = JudgeService()

def get_judge_service():
    """获取判题服务实例"""
    return judge_service

import sys
sys.path.append('backend')

from flask import Flask
from models import *
from database import db
from questions import get_question
import json

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = (
    'mssql+pyodbc://localhost/OnlineJudge?trusted_connection=yes&driver=ODBC+Driver+17+for+SQL+Server&trusted_connection=yes&TrustServerCertificate=yes')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

app.config['DB_CONNECTION_POOLS'] = {
    'default': {
        'host': 'localhost',
        'port': 1433,
        'user': 'admin',
        'password': 'admin',
        'database': 'OnlineJudge',
        'uri': 'mssql+pyodbc://localhost/OnlineJudge?trusted_connection=yes&driver=ODBC+Driver+17+for+SQL+Server&trusted_connection=yes&TrustServerCertificate=yes',
        'pool_size': 5,
        'max_overflow': 10
    }
}

db.init_app(app)

def test_problem_detail():
    """测试题目详情API"""
    with app.app_context():
        try:
            print("=== 测试题目详情API ===")
            
            # 测试获取第一道题目的详情
            question_id = 73  # 从之前的查询结果知道第一道题目的ID是73
            
            print(f"正在获取题目ID {question_id} 的详情...")
            
            # 调用API函数
            response = get_question(question_id)
            
            print(f"API响应类型: {type(response)}")
            
            # 如果是Flask Response对象，获取数据
            if hasattr(response, 'get_data'):
                data = response.get_data(as_text=True)
                print(f"响应数据: {data}")
                
                # 解析JSON
                try:
                    json_data = json.loads(data)
                    print(f"解析后的JSON: {json_data}")
                    
                    # 检查必要字段
                    required_fields = ['id', 'title', 'difficulty', 'tags', 'description']
                    for field in required_fields:
                        if field in json_data:
                            print(f"✓ {field}: {json_data[field]}")
                        else:
                            print(f"✗ 缺少字段: {field}")
                            
                except json.JSONDecodeError as e:
                    print(f"JSON解析失败: {e}")
            
            print("=== 测试完成 ===")
            return True
            
        except Exception as e:
            print(f"=== 测试失败 ===")
            print(f"错误信息: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = test_problem_detail()
    if success:
        print("\n✅ 题目详情API测试成功")
    else:
        print("\n❌ 题目详情API测试失败")

from flask import jsonify  # 确保导入了 jsonify
from models import *
from database import db
from sqlalchemy import and_
import traceback

def get_selected_questions(filter):
    try:
        print("处理题目过滤请求:", filter)

        # 获取分页参数
        pg = filter.get("page", 1)
        ppg = filter.get("per_page", 15)
        tag = filter.get("tag")
        difficulty = filter.get("difficulty")
        search = filter.get("search")

        print(f"分页参数: page={pg}, per_page={ppg}")
        print(f"过滤条件: tag={tag}, difficulty={difficulty}, search={search}")

        # 构建基础查询
        query = Questions.query
        print(f"基础查询创建成功")

        # 添加过滤条件
        if tag and tag != "全部":
            query = query.filter(Questions.QuesType.ilike(f'%{tag}%'))
            print(f"添加标签过滤: {tag}")

        if difficulty and difficulty != "全部":
            query = query.filter(Questions.QuesLevel == difficulty)
            print(f"添加难度过滤: {difficulty}")

        if search and search.strip():
            search_term = search.strip()
            print(f"添加搜索过滤: {search_term}")
            # 支持按题目名称或ID搜索
            try:
                # 尝试按ID搜索
                question_id = int(search_term)
                query = query.filter(Questions.QuesID == question_id)
                print(f"按ID搜索: {question_id}")
            except ValueError:
                # 按题目名称模糊搜索
                query = query.filter(Questions.QuesName.ilike(f'%{search_term}%'))
                print(f"按名称搜索: {search_term}")

        print("开始执行分页查询...")
        # 添加排序（MSSQL分页查询必须有ORDER BY）
        query = query.order_by(Questions.QuesID)

        # 执行分页查询
        paginated_questions = query.paginate(
            page=pg,
            per_page=ppg,
            error_out=False
        )
        print(f"分页查询完成，获得 {len(paginated_questions.items)} 条记录")

        # 格式化返回数据
        problems = []
        for question in paginated_questions.items:
            problems.append({
                'id': question.QuesID,
                'title': question.QuesName,
                'difficulty': question.QuesLevel,
                'tags': question.QuesType,
                'accepted': question.PassNum,
                'submitted': question.SubNum
            })

        result = {
            'problems': problems,
            'total': paginated_questions.total,
            'current_page': paginated_questions.page,
            'per_page': paginated_questions.per_page,
            'total_pages': paginated_questions.pages
        }

        print(f"从数据库查询到 {len(problems)} 道题目，总共 {paginated_questions.total} 道题目")
        print(f"返回结果: {result}")
        return jsonify(result)

    except Exception as e:
        print(f"查询题目列表时出错: {str(e)}")
        print(traceback.format_exc())  # 打印完整堆栈跟踪

        # 如果数据库查询失败，返回测试数据作为备用
        test_data = {
            'problems': [
                {
                    'id': 1,
                    'title': '两数之和 (测试数据)',
                    'difficulty': '简单',
                    'tags': '数组',
                    'accepted': 5,
                    'submitted': 10
                },
                {
                    'id': 2,
                    'title': '有效的括号 (测试数据)',
                    'difficulty': '中等',
                    'tags': '栈',
                    'accepted': 3,
                    'submitted': 7
                }
            ],
            'total': 2,
            'error': f'数据库查询失败，显示测试数据: {str(e)}'
        }

        print("返回测试数据:", test_data)
        return jsonify(test_data)



def get_question(question_id):
    try:
        print(f"正在查询题目ID: {question_id}")
        question = Questions.query.get(question_id)

        if not question:
            print(f"题目ID {question_id} 不存在")
            return jsonify({'error': f'题目ID {question_id} 不存在'}), 404

        print(f"找到题目: {question.QuesName}")

        result = {
            'id': question.QuesID,
            'title': question.QuesName,
            'difficulty': question.QuesLevel,
            'tags': question.QuesType,
            'description': question.QuesDescrip,
            'inputFormat': question.IptFomart,
            'outputFormat': question.OptFomart,
            'inputExample': question.IptExample,
            'outputExample': question.OptExample
        }

        print(f"返回题目数据: {result}")
        return jsonify(result)

    except Exception as e:
        print(f"查询题目时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

def sub_question(sub_data):
    try:
        data = sub_data
        if not data:
            return jsonify({"error": "No data provided"}), 400

        new_sub=Submissions(
            UserId = data.get("user_id").strip(),
            QuesID = data.get("question_id"),
            SubContent = data.get("code"),
            PassStatus = 'Judging'
        )

        db.session.add(new_sub)
        db.session.commit()

    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500

def update_question(data):
    question_id=data.get("id")
    question=Questions.query.get(question_id)
    if question:
        question.QuesType=data.get("tags")
        question.QuesLevel=data.get("difficulty")
        question.QuesName=data.get("title")
        question.QuesDescrip=data.get("description")
        question.IptFomart=data.get("inputFormat")
        question.OptFomart=data.get("outputFormat")
        question.IptExample=data.get("inputExample")
        question.OptExample=data.get("outputExample")
    else:
        new_question = Questions(
                QuesType=data.get("tags"),
                QuesLevel=data.get("difficulty", "medium"),
                QuesName=data.get("title", "Untitled Question"),
                QuesDescrip=data.get("description", ""),
                IptFomart=data.get("inputFormat", ""),
                OptFomart=data.get("outputFormat", ""),
                IptExample=data.get("inputExample", ""),
                OptExample=data.get("outputExample", ""),
                SubNum=0,  # 初始化提交数
                CollNum=0,  # 初始化收藏数
                PassNum=0   # 初始化通过数
            )
        db.session.add(new_question)
    db.session.commit()
    return jsonify({"message":"update question successfully"})

def remove_question(data):
    id=data.get('id')
    q=Questions.query.get(id)
    db.session.delete(q)
    db.session.commit()

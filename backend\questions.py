from flask import jsonify
from models import *
from database import db

def get_selected_questions(filter):
    try:
        # 获取分页参数
        pg = filter.get("page", 1)
        ppg = filter.get("per_page", 15)
        tag = filter.get("tag")
        difficulty = filter.get("difficulty")
        search = filter.get("search")

        # 构建基础查询
        query = Questions.query

        # 添加过滤条件
        if tag and tag != "全部":
            query = query.filter(Questions.QuesType.ilike(f'%{tag}%'))

        if difficulty and difficulty != "全部":
            query = query.filter(Questions.QuesLevel == difficulty)

        if search and search.strip():
            search_term = search.strip()
            # 支持按题目名称或ID搜索
            try:
                # 尝试按ID搜索
                question_id = int(search_term)
                query = query.filter(Questions.QuesID == question_id)
            except ValueError:
                # 按题目名称模糊搜索
                query = query.filter(Questions.QuesName.ilike(f'%{search_term}%'))

        # 添加排序（MSSQL分页查询必须有ORDER BY）
        query = query.order_by(Questions.QuesID)

        # 执行分页查询
        paginated_questions = query.paginate(
            page=pg,
            per_page=ppg,
            error_out=False
        )

        # 格式化返回数据
        problems = []
        for question in paginated_questions.items:
            problems.append({
                'id': question.QuesID,
                'title': question.QuesName,
                'difficulty': question.QuesLevel,
                'tags': question.QuesType,
                'accepted': question.PassNum,
                'submitted': question.SubNum
            })

        result = {
            'problems': problems,
            'total': paginated_questions.total,
            'current_page': paginated_questions.page,
            'per_page': paginated_questions.per_page,
            'total_pages': paginated_questions.pages
        }

        return jsonify(result)

    except Exception as e:
        return jsonify({'error': str(e)}), 500



def get_question(question_id):
    try:
        question = Questions.query.get(question_id)

        if not question:
            return jsonify({'error': f'题目ID {question_id} 不存在'}), 404

        result = {
            'id': question.QuesID,
            'title': question.QuesName,
            'difficulty': question.QuesLevel,
            'tags': question.QuesType,
            'description': question.QuesDescrip,
            'inputFormat': question.IptFomart,
            'outputFormat': question.OptFomart,
            'inputExample': question.IptExample,
            'outputExample': question.OptExample
        }

        return jsonify(result)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

def sub_question(sub_data):
    try:
        data = sub_data
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # 创建新的提交记录
        new_sub = Submissions(
            UserID=data.get("user_id"),
            QuesID=data.get("question_id"),
            SubContent=data.get("code"),
            PassStatus='Accepted'  # 暂时直接设为通过，用于测试
        )

        db.session.add(new_sub)
        db.session.commit()

        return jsonify({
            "message": "代码提交成功！",
            "submission_id": new_sub.SubID,
            "status": "Accepted"
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500

def update_question(data):
    question_id=data.get("id")
    question=Questions.query.get(question_id)
    if question:
        question.QuesType=data.get("tags")
        question.QuesLevel=data.get("difficulty")
        question.QuesName=data.get("title")
        question.QuesDescrip=data.get("description")
        question.IptFomart=data.get("inputFormat")
        question.OptFomart=data.get("outputFormat")
        question.IptExample=data.get("inputExample")
        question.OptExample=data.get("outputExample")
    else:
        new_question = Questions(
                QuesType=data.get("tags"),
                QuesLevel=data.get("difficulty", "medium"),
                QuesName=data.get("title", "Untitled Question"),
                QuesDescrip=data.get("description", ""),
                IptFomart=data.get("inputFormat", ""),
                OptFomart=data.get("outputFormat", ""),
                IptExample=data.get("inputExample", ""),
                OptExample=data.get("outputExample", ""),
                SubNum=0,  # 初始化提交数
                CollNum=0,  # 初始化收藏数
                PassNum=0   # 初始化通过数
            )
        db.session.add(new_question)
    db.session.commit()
    return jsonify({"message":"update question successfully"})

def remove_question(data):
    id=data.get('id')
    q=Questions.query.get(id)
    db.session.delete(q)
    db.session.commit()

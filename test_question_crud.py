import requests
import json

# 测试题目CRUD操作
def test_create_question():
    url = "http://127.0.0.1:5000/api/admin/questions?action=update_question"
    headers = {
        'Authorization': 'Bearer test_token',
        'Content-Type': 'application/json'
    }
    
    # 测试创建新题目
    print("测试创建新题目...")
    data = {
        "title": "测试题目",
        "difficulty": "easy",
        "description": "这是一个测试题目的描述",
        "tags": ["算法", "基础"],
        "inputDescription": "输入描述",
        "outputDescription": "输出描述",
        "examples": [
            {
                "input": "1 2",
                "output": "3"
            }
        ],
        "testCases": [
            {
                "input": "1 2",
                "output": "3"
            }
        ]
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 201:
            response_data = response.json()
            question_id = response_data.get("question_id")
            print(f"创建成功，题目ID: {question_id}")
            return question_id
        else:
            print("创建失败")
            return None
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def test_update_question(question_id):
    if not question_id:
        print("没有有效的题目ID，跳过更新测试")
        return
        
    url = "http://127.0.0.1:5000/api/admin/questions?action=update_question"
    headers = {
        'Authorization': 'Bearer test_token',
        'Content-Type': 'application/json'
    }
    
    # 测试更新题目
    print(f"\n测试更新题目 ID: {question_id}...")
    data = {
        "id": question_id,
        "title": "更新后的测试题目",
        "difficulty": "medium",
        "description": "这是更新后的题目描述",
        "tags": ["算法", "进阶"],
        "inputDescription": "更新后的输入描述",
        "outputDescription": "更新后的输出描述",
        "examples": [
            {
                "input": "2 3",
                "output": "5"
            }
        ]
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    # 测试创建题目
    question_id = test_create_question()
    
    # 测试更新题目
    test_update_question(question_id)

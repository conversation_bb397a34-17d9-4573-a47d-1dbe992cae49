# 测试题解管理API
Write-Host "测试题解管理API..."

# 1. 测试GET请求获取题解列表
Write-Host "1. 测试GET请求获取题解列表..."
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/solutions?pg=1&ppg=10" -Method GET -Headers @{'Authorization'='Bearer test_token'}
    Write-Host "✅ GET请求成功: $($response.StatusCode)"
    $data = $response.Content | ConvertFrom-Json
    Write-Host "题解数量: $($data.solutions.Count)"
    Write-Host "总数: $($data.total)"
} catch {
    Write-Host "❌ GET请求失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)"
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

Write-Host ""

# 2. 测试题目列表API（用于题解关联）
Write-Host "2. 测试题目列表API（用于题解关联）..."
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/problems?action=get_solutions&per_page=100" -Method GET
    Write-Host "✅ 题目列表API成功: $($response.StatusCode)"
    $data = $response.Content | ConvertFrom-Json
    Write-Host "题目数量: $($data.problems.Count)"
    Write-Host "总数: $($data.total)"
} catch {
    Write-Host "❌ 题目列表API失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)"
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

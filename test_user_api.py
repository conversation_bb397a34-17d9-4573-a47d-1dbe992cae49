import sys
sys.path.append('backend')

from flask import Flask
from models import *
from database import db
from others import get_userinfo, get_collections
import json

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = (
    'mssql+pyodbc://localhost/OnlineJudge?trusted_connection=yes&driver=ODBC+Driver+17+for+SQL+Server&trusted_connection=yes&TrustServerCertificate=yes')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

app.config['DB_CONNECTION_POOLS'] = {
    'default': {
        'host': 'localhost',
        'port': 1433,
        'user': 'admin',
        'password': 'admin',
        'database': 'OnlineJudge',
        'uri': 'mssql+pyodbc://localhost/OnlineJudge?trusted_connection=yes&driver=ODBC+Driver+17+for+SQL+Server&trusted_connection=yes&TrustServerCertificate=yes',
        'pool_size': 5,
        'max_overflow': 10
    }
}

db.init_app(app)

def test_user_api():
    """测试用户API"""
    with app.app_context():
        try:
            print("=== 测试用户API ===")
            
            # 首先查看有哪些用户
            users = Users.query.limit(3).all()
            print(f"数据库中的用户:")
            for user in users:
                print(f"  ID: {user.UserID}, 用户名: {user.UserName}")
            
            if not users:
                print("数据库中没有用户")
                return False
            
            # 测试第一个用户的信息
            test_user_id = users[0].UserID
            print(f"\n正在测试用户ID {test_user_id} 的信息...")
            
            # 测试获取用户信息
            response = get_userinfo(test_user_id)
            print(f"用户信息API响应类型: {type(response)}")
            
            if hasattr(response, 'get_data'):
                data = response.get_data(as_text=True)
                print(f"用户信息响应数据: {data}")
                
                try:
                    json_data = json.loads(data)
                    print(f"解析后的用户信息: {json_data}")
                    
                    # 检查必要字段
                    required_fields = ['username', 'joinDate', 'avatar', 'solved', 'submitted', 'accuracy']
                    for field in required_fields:
                        if field in json_data:
                            print(f"✓ {field}: {json_data[field]}")
                        else:
                            print(f"✗ 缺少字段: {field}")
                            
                except json.JSONDecodeError as e:
                    print(f"JSON解析失败: {e}")
            
            # 测试获取收藏夹
            print(f"\n正在测试用户ID {test_user_id} 的收藏夹...")
            response = get_collections(test_user_id)
            print(f"收藏夹API响应类型: {type(response)}")
            
            if hasattr(response, 'get_data'):
                data = response.get_data(as_text=True)
                print(f"收藏夹响应数据: {data}")
                
                try:
                    json_data = json.loads(data)
                    print(f"解析后的收藏夹: {json_data}")
                    
                    if isinstance(json_data, list):
                        print(f"收藏夹中有 {len(json_data)} 个题目")
                        for item in json_data[:3]:  # 只显示前3个
                            print(f"  题目ID: {item.get('problem_id')}, 标题: {item.get('title')}")
                    else:
                        print("收藏夹数据格式不正确")
                            
                except json.JSONDecodeError as e:
                    print(f"JSON解析失败: {e}")
            
            print("=== 测试完成 ===")
            return True
            
        except Exception as e:
            print(f"=== 测试失败 ===")
            print(f"错误信息: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = test_user_api()
    if success:
        print("\n✅ 用户API测试成功")
    else:
        print("\n❌ 用户API测试失败")

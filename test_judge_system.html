<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>判题系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        textarea {
            height: 200px;
            font-family: 'Courier New', monospace;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <h1>判题系统测试</h1>
    
    <form id="submitForm">
        <div class="form-group">
            <label for="questionId">题目ID:</label>
            <select id="questionId" required>
                <option value="1">1 - 两数之和</option>
                <option value="2">2 - 有效的括号</option>
                <option value="3">3 - 简单加法</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="language">编程语言:</label>
            <select id="language" required>
                <option value="python">Python</option>
                <option value="cpp">C++</option>
                <option value="java">Java</option>
                <option value="c">C</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="code">代码:</label>
            <textarea id="code" placeholder="请输入你的代码..." required></textarea>
        </div>
        
        <button type="submit">提交代码</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        // 示例代码模板
        const codeTemplates = {
            python: {
                1: `# 两数之和
nums = list(map(int, input().split()))
target = int(input())

for i in range(len(nums)):
    for j in range(i + 1, len(nums)):
        if nums[i] + nums[j] == target:
            print(i, j)
            break`,
                2: `# 有效的括号
s = input().strip()
stack = []
mapping = {')': '(', '}': '{', ']': '['}

for char in s:
    if char in mapping:
        if not stack or stack.pop() != mapping[char]:
            print('false')
            exit()
    else:
        stack.append(char)

print('true' if not stack else 'false')`,
                3: `# 简单加法
a, b = map(int, input().split())
print(a + b)`
            },
            cpp: {
                1: `#include <iostream>
#include <vector>
using namespace std;

int main() {
    vector<int> nums;
    int num, target;
    
    // 读取数组
    string line;
    getline(cin, line);
    // 简化处理，假设输入格式正确
    
    cin >> target;
    
    // 暴力解法
    for (int i = 0; i < nums.size(); i++) {
        for (int j = i + 1; j < nums.size(); j++) {
            if (nums[i] + nums[j] == target) {
                cout << i << " " << j << endl;
                return 0;
            }
        }
    }
    return 0;
}`,
                3: `#include <iostream>
using namespace std;

int main() {
    int a, b;
    cin >> a >> b;
    cout << a + b << endl;
    return 0;
}`
            }
        };
        
        // 当选择改变时更新代码模板
        document.getElementById('questionId').addEventListener('change', updateCodeTemplate);
        document.getElementById('language').addEventListener('change', updateCodeTemplate);
        
        function updateCodeTemplate() {
            const questionId = document.getElementById('questionId').value;
            const language = document.getElementById('language').value;
            const codeTextarea = document.getElementById('code');
            
            if (codeTemplates[language] && codeTemplates[language][questionId]) {
                codeTextarea.value = codeTemplates[language][questionId];
            }
        }
        
        // 初始化代码模板
        updateCodeTemplate();
        
        document.getElementById('submitForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const questionId = document.getElementById('questionId').value;
            const language = document.getElementById('language').value;
            const code = document.getElementById('code').value;
            const resultDiv = document.getElementById('result');
            
            // 显示提交中状态
            resultDiv.innerHTML = '<div class="info">正在提交代码...</div>';
            
            try {
                // 提交代码
                const response = await fetch('http://127.0.0.1:5000/questions/1?action=submit_question', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test_token'  // 测试用token
                    },
                    body: JSON.stringify({
                        user_id: 1,
                        question_id: parseInt(questionId),
                        code: code,
                        language: language
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">代码提交成功！提交ID: ${result.submission_id}</div>`;
                    
                    // 轮询检查判题结果
                    checkJudgeResult(result.submission_id);
                } else {
                    resultDiv.innerHTML = `<div class="error">提交失败: ${result.error}</div>`;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
            }
        });
        
        async function checkJudgeResult(submissionId) {
            const resultDiv = document.getElementById('result');
            let attempts = 0;
            const maxAttempts = 30; // 最多检查30次
            
            const checkInterval = setInterval(async () => {
                attempts++;
                
                try {
                    const response = await fetch(`http://127.0.0.1:5000/api/submission/${submissionId}/status`);
                    const result = await response.json();
                    
                    if (response.ok) {
                        if (result.status !== 'Judging') {
                            // 判题完成
                            clearInterval(checkInterval);
                            displayJudgeResult(result);
                        } else {
                            // 仍在判题中
                            resultDiv.innerHTML = `<div class="info">正在判题中... (${attempts}/${maxAttempts})</div>`;
                        }
                    }
                    
                    if (attempts >= maxAttempts) {
                        clearInterval(checkInterval);
                        resultDiv.innerHTML += '<div class="error">判题超时，请稍后手动查询结果</div>';
                    }
                    
                } catch (error) {
                    console.error('检查判题结果失败:', error);
                }
            }, 1000); // 每秒检查一次
        }
        
        function displayJudgeResult(result) {
            const resultDiv = document.getElementById('result');
            const statusClass = result.status === 'Accepted' ? 'success' : 'error';
            
            resultDiv.innerHTML = `
                <div class="${statusClass}">
                    <h3>判题结果</h3>
                    <p><strong>状态:</strong> ${result.status}</p>
                    <p><strong>消息:</strong> ${result.message}</p>
                    <p><strong>语言:</strong> ${result.language}</p>
                    <p><strong>提交时间:</strong> ${result.submit_time}</p>
                    ${result.execution_time ? `<p><strong>执行时间:</strong> ${result.execution_time}ms</p>` : ''}
                    ${result.memory_usage ? `<p><strong>内存使用:</strong> ${result.memory_usage}KB</p>` : ''}
                </div>
            `;
        }
    </script>
</body>
</html>

# 测试题目管理API
Write-Host "测试题目管理API..."

# 1. 测试题目筛选API
Write-Host "1. 测试题目筛选API..."
try {
    $body = @{
        params = @{
            page = 1
            per_page = 10
        }
    } | ConvertTo-Json -Depth 3
    
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/questions/filter" -Method POST -Body $body -ContentType "application/json"
    Write-Host "✅ 题目筛选API测试: $($response.StatusCode)"
    $data = $response.Content | ConvertFrom-Json
    Write-Host "题目数量: $($data.problems.Count)"
    Write-Host "总数: $($data.total)"
    if ($data.problems.Count -gt 0) {
        Write-Host "第一个题目: $($data.problems[0].title)"
    }
} catch {
    Write-Host "❌ 题目筛选API测试失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

Write-Host ""

# 2. 测试题目列表API（用于题解管理）
Write-Host "2. 测试题目列表API（用于题解管理）..."
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/problems?action=get_solutions&per_page=100" -Method GET
    Write-Host "✅ 题目列表API测试: $($response.StatusCode)"
    $data = $response.Content | ConvertFrom-Json
    Write-Host "题目数量: $($data.problems.Count)"
    Write-Host "总数: $($data.total)"
    if ($data.problems.Count -gt 0) {
        Write-Host "第一个题目: $($data.problems[0].title)"
    }
} catch {
    Write-Host "❌ 题目列表API测试失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

<template>
  <div class="admin-container">
    <TopNav />
    <div class="admin-main">
      <nav class="admin-sidebar">
        <ul>
          <li>
            <router-link to="/admin/users">
              <el-icon><User /></el-icon> 用户管理
            </router-link>
          </li>
          <li>
            <router-link to="/admin/problems">
              <el-icon><EditPen /></el-icon> 题目管理
            </router-link>
          </li>
          <li>
            <router-link to="/admin/solutions">
              <el-icon><Document /></el-icon> 题解管理
            </router-link>
          </li>
          <li>
            <router-link to="/admin/comments">
              <el-icon><ChatSquare /></el-icon> 评论管理
            </router-link>
          </li>
        </ul>
      </nav>
      <div class="admin-content">
        <div class="admin-action-bar">
          <span style="font-size: 1.1rem; font-weight: bold; color: #4f46e5">
            管理后台
          </span>
        </div>
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import TopNav from "@/components/TopNav.vue";
import { User, EditPen, Document, ChatSquare } from "@element-plus/icons-vue";
</script>

<style scoped>
.admin-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8fafc;
}

.admin-main {
  display: flex;
  flex: 1;
  min-height: 0;
}

.admin-sidebar {
  width: 220px;
  background: #2d3748;
  color: #fff;
  padding: 24px 0;
  height: 100%;
  box-shadow: 2px 0 8px 0 rgba(0, 0, 0, 0.03);
}

.admin-sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.admin-sidebar li {
  padding: 14px 32px;
}

.admin-sidebar li a {
  color: #fff;
  text-decoration: none;
  display: flex;
  align-items: center;
  border-radius: 8px;
  transition: background 0.2s;
  gap: 0.5em;
  font-size: 1rem;
}

.admin-sidebar li a.router-link-exact-active,
.admin-sidebar li a.router-link-active {
  background: #4f46e5;
}

.admin-content {
  flex: 1;
  padding: 32px 24px;
  background: #f8fafc;
  overflow-y: auto;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.admin-action-bar {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 1.2rem;
  min-height: 2.5rem;
}
</style>

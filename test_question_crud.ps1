# 测试题目CRUD操作
Write-Host "测试题目CRUD操作..."

# 测试创建新题目
Write-Host "1. 测试创建新题目..."
$createBody = @{
    title = "测试题目"
    difficulty = "easy"
    description = "这是一个测试题目的描述"
    tags = @("算法", "基础")
    inputDescription = "输入描述"
    outputDescription = "输出描述"
    examples = @(
        @{
            input = "1 2"
            output = "3"
        }
    )
    testCases = @(
        @{
            input = "1 2"
            output = "3"
        }
    )
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/admin/questions?action=update_question" -Method POST -Headers @{'Authorization'='Bearer test_token'; 'Content-Type'='application/json'} -Body $createBody
    Write-Host "✅ 创建题目测试: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
    
    # 尝试解析响应获取题目ID
    $responseData = $response.Content | ConvertFrom-Json
    $questionId = $responseData.question_id
    Write-Host "题目ID: $questionId"
} catch {
    Write-Host "❌ 创建题目测试失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

Write-Host ""

# 测试更新题目（使用固定ID）
Write-Host "2. 测试更新题目..."
$updateBody = @{
    id = 1  # 使用已存在的题目ID
    title = "更新后的测试题目"
    difficulty = "medium"
    description = "这是更新后的题目描述"
    tags = @("算法", "进阶")
    inputDescription = "更新后的输入描述"
    outputDescription = "更新后的输出描述"
    examples = @(
        @{
            input = "2 3"
            output = "5"
        }
    )
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5000/api/admin/questions?action=update_question" -Method POST -Headers @{'Authorization'='Bearer test_token'; 'Content-Type'='application/json'} -Body $updateBody
    Write-Host "✅ 更新题目测试: $($response.StatusCode)"
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "❌ 更新题目测试失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody"
    }
}

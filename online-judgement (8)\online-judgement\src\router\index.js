import { createRouter, createWebHistory } from "vue-router";

const routes = [
  { path: "/", redirect: "/login" },
  {
    path: "/login",
    name: "login",
    component: () => import("@/views/login.vue"),
  },
  {
    path: "/register",
    name: "register",
    component: () => import("@/views/register.vue"),
  },
  {
    path: "/welcome",
    name: "welcome",
    component: () => import("@/views/welcome.vue"),
    meta: { requiresAuth: true },
  },
  {
    path: "/home",
    name: "home",
    component: () => import("@/views/home.vue"),
    meta: { requiresAuth: true },
  },
  {
    path: "/problem/:id",
    name: "problemPage",
    component: () => import("@/views/ProblemPage.vue"),
    meta: { requiresAuth: true },
  },
  // 添加题解页面路由
  {
    path: "/solution/:id",
    name: "solutionPage",
    component: () => import("@/views/SolutionPage.vue"),
    meta: { requiresAuth: true },
  },
  {
    path: "/profile",
    name: "Profile",
    component: () => import("@/views/UserProfile.vue"),
    meta: { requiresAuth: true },
  },
  {
    path: "/ranking",
    name: "ranking",
    component: () => import("@/views/Ranking.vue"),
    meta: { requiresAuth: true },
  },
  // 管理员相关路由
  {
    path: "/admin",
    name: "admin",
    component: () => import("@/views/AdminPage.vue"),
    meta: { requiresAuth: true, requiresAdmin: true },
    children: [
      {
        path: "users",
        name: "adminUsers",
        component: () => import("@/views/admin/UserManagement.vue"),
      },
      {
        path: "problems",
        name: "adminProblems",
        component: () => import("@/views/admin/ProblemManagement.vue"),
      },
      {
        path: "solutions",
        name: "adminSolutions",
        component: () => import("@/views/admin/SolutionManagement.vue"),
      },
      {
        path: "comments",
        name: "adminComments",
        component: () => import("@/views/admin/CommentManagement.vue"),
      },
      // 默认重定向到用户管理
      { path: "", redirect: "/admin/users" },
    ],
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

router.beforeEach((to, from, next) => {
  const isLoggedIn = !!localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo") || "{}");
  const isAdmin = userInfo.userType === "admin";

  if (to.meta.requiresAuth && !isLoggedIn) {
    next({ path: "/login", query: { redirect: to.fullPath } });
  } else {
    next();
  }
});

export default router;

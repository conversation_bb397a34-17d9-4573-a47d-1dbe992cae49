from flask import Flask,request,jsonify,g
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import *
from functools import wraps
import re
import jwt
from flask_cors import CORS

from models import *
from database import db
from datetime import datetime, timezone,timedelta
from questions import *
from feedback import *
from reviews import *
from others import *
from account import *

app = Flask(__name__)
CORS(app)
# 配置数据库连接URI
app.config['SQLALCHEMY_DATABASE_URI'] = (
    'mssql+pyodbc://localhost/OnlineJudge?trusted_connection=yes&driver=ODBC+Driver+17+for+SQL+Server&trusted_connection=yes&TrustServerCertificate=yes')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SECRET_KEY'] = 'your-secret-key-here'  # 生产环境应该使用更安全的密钥
app.config['JWT_EXPIRATION_DELTA'] = timedelta(hours=1)  # Token过期时间为1小时

app.config['DB_CONNECTION_POOLS'] = {
    'default': {
        'host': 'localhost',
        'port': 1433,
        'user': 'admin',
        'password': 'admin',
        'database': 'OnlineJudge',
        'uri': 'mssql+pyodbc://localhost/OnlineJudge?trusted_connection=yes&driver=ODBC+Driver+17+for+SQL+Server&trusted_connection=yes&TrustServerCertificate=yes',
        'pool_size': 5,         # 连接池大小
        'max_overflow': 10      # 最大溢出连接数# 其他连接池配置
    }
}

db.init_app(app)


def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization', '').split(' ')[-1]  # Bearer <token>

        if not token:
            return jsonify({"error": "Token缺失"}), 401

        try:
            payload = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            g.user_id = payload['sub']  # 存储当前用户ID
        except jwt.ExpiredSignatureError:
            return jsonify({"error": "Token已过期"}), 401
        except jwt.InvalidTokenError:
            return jsonify({"error": "无效Token"}), 401

        return f(*args, **kwargs)
    return decorated

def levels_required(allowed_levels):
    """验证用户权限级别的装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 从token中获取用户级别
            token = request.headers.get('Authorization', '').split(' ')[-1]
            if not token:
                return jsonify({"error": "Token缺失"}), 401

            try:
                payload = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
                user_level = payload.get('level', 'user')
                g.user_id = payload['sub']  # 存储当前用户ID
                g.user_level = user_level   # 存储当前用户级别

                if user_level not in allowed_levels:
                    return jsonify({'message': '权限不足'}), 403

            except jwt.ExpiredSignatureError:
                return jsonify({"error": "Token已过期"}), 401
            except jwt.InvalidTokenError:
                return jsonify({"error": "无效Token"}), 401

            return f(*args, **kwargs)
        return decorated_function
    return decorator


@app.route("/api/questions/filter", methods=["POST"])
def filter_questions():
    """处理题目过滤请求"""
    try:
        # 获取请求数据
        filter_data = request.json

        # 调用处理函数
        return get_selected_questions(filter_data)
    except Exception as e:
        return jsonify({
            'error': str(e),
            'problems': [],
            'total': 0
        }), 500



@app.route("/questions/<int:question_id>", methods=["GET", "POST"])
@token_required
@levels_required(['admin', 'user'])
def single_question(question_id):
    if request.method == "GET":
        action = request.args.get("action")
        if action =="get_question":                 # 获取题目信息
            return get_question(question_id)
        elif action=="get_review":                  # 获取评论信息
            return get_reviews(question_id)
    elif request.method == "POST":
        action = request.args.get("action")
        if action == "submit_question":             # 提交答案
            return sub_question(request.get_json())
        elif action == "submit_review":             # 提交评论
            return sub_review(request.get_json())
        elif action == "submit_qsfeedback":         # 提交题目反馈
            return sub_qsfeedback(request.get_json())
        elif action == "submit_rvfeedback":         # 提交评论反馈
            return sub_rvfeedback(request.get_json())
        elif action =='check_favorite':             # 检查收藏状态
            return check_favorite(request.get_json())
        elif action =='remove_favorite':            # 删除收藏
            return remove_favorite(request.get_json())
        elif action =='add_favorite':               # 添加收藏
            return add_favorite(request.get_json())
    else:
        return jsonify({"error": "Invalid action"}), 400

# 添加API路径的题目详情接口
@app.route("/api/questions/<int:question_id>", methods=["GET", "POST"])
@token_required
@levels_required(['admin', 'user'])
def api_single_question(question_id):
    if request.method == "GET":
        action = request.args.get("action")
        if action =="get_question":                 # 获取题目信息
            return get_question(question_id)
        elif action=="get_review":                  # 获取评论信息
            return get_reviews(question_id)
    elif request.method == "POST":
        action = request.args.get("action")
        if action == "submit_question":             # 提交答案
            return sub_question(request.get_json())
        elif action == "submit_review":             # 提交评论
            return sub_review(request.get_json())
        elif action == "submit_qsfeedback":         # 提交题目反馈
            return sub_qsfeedback(request.get_json())
        elif action == "submit_rvfeedback":         # 提交评论反馈
            return sub_rvfeedback(request.get_json())
        elif action =='check_favorite':             # 检查收藏状态
            return check_favorite(request.get_json())
        elif action =='remove_favorite':            # 删除收藏
            return remove_favorite(request.get_json())
        elif action =='add_favorite':               # 添加收藏
            return add_favorite(request.get_json())
    else:
        return jsonify({"error": "Invalid action"}), 400

@app.route("/admin/questions", methods=["POST"])
@token_required
@levels_required('admin')
def question_manage():
    action=request.args.get("action")
    if action=='update_question':
        return update_question(request.get_json())
    elif action=='remove_question':
        return remove_question(request.get_json())


@app.route("/api/login",methods=["POST"])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')

    if not username or not password:
        return jsonify({"token":None,
        "user": {
            "id": None,
            "username": username,
            "type": None
        },
        "error":"用户名或密码不能为空"}),400

    user = Users.query.filter_by(UserName=username).first()

    # 开发模式：允许使用明文密码（仅用于测试）
    if user and user.Password == password:
        password_match = True
    else:
        password_match = user and check_password_hash(user.Password, password)

    if not user or not password_match:
        return jsonify({"token":None,
            "user": {
                "id": None,
                "username": username,
                "type": None
            },
            "error":"用户名或密码错误"}),401

    # 根据用户级别设置数据库引擎
    if user.Level == 'guest' or user.Status == 'banned':
        g.db_engine = db.get_engine('guest')
    elif user.Level == 'user' and user.Status == 'active':
        g.db_engine = db.get_engine('user')
    elif user.Level == 'admin':
        g.db_engine = db.get_engine('admin')

    # 生成Token
    token = generate_token(user.UserID, user.UserName, user.Level)

    return jsonify({"token": token,
        "user": {
            "id": user.UserID,
            "username": user.UserName,
            "type": user.Level
        },
        "error":""})


@app.route("/api/register", methods=["POST"])
def register():
    try:
        # 1. 获取并验证JSON数据
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # 2. 提取并清理数据
        username = data.get("username").strip()
        password = data.get("password")

        # 3. 验证必要字段
        if not username or not password:
            return jsonify({"error": "用户名或密码不能为空"}), 400

        # 4. 验证数据格式
        # 用户名：4-20位字母数字
        if not re.match(r'^[a-zA-Z0-9]{4,20}$', username):
            return jsonify({"error": "Username must be 4-20 alphanumeric characters"}), 400

        # 密码强度：至少8位，包含字母和数字
        if len(password) < 8 or not re.search(r'\d', password) or not re.search(r'[a-zA-Z]', password):
            return jsonify({
                "error": "Password must be at least 8 characters with both letters and numbers"
            }), 400

        # 5. 检查用户名和邮箱是否已存在
        if Users.query.filter_by(UserName=username).first():
            return jsonify({"error": "Username already exists"}), 409

        # 6. 创建新用户
        new_user = Users(
            UserName=username,
            Password=generate_password_hash(password),  # 密码加密
            Status='active',  # 默认状态
            Level='user',     # 默认级别
            RegistrationTime=datetime.now(timezone.utc)  # 使用utc时间
        )

        db.session.add(new_user)
        db.session.commit()

        # 7. 返回成功响应（不包含敏感信息）
        return jsonify({
            "message": "User registered successfully",
            "user": {
                "UserID": new_user.UserID,
                "RegistrationTime": new_user.RegistrationTime.isoformat()
            }
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500

@app.route("/api/rank", methods=["GET"])
def rank():
    try:
        return get_rank()
    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500

@app.route("/api/user/<int:requested_user_id>", methods=["GET","POST"])
@token_required
@levels_required(['admin', 'user'])
def user_page(requested_user_id):
    try:
        if requested_user_id==g.user_id:
            if request.method == "GET":
                action=request.args.get("action")
                if action=='get_userinfo':
                    return get_userinfo(requested_user_id)
                elif action=='get_collections':
                    return get_collections(requested_user_id)
            elif request.method == "POST":
                action=request.args.get("action")
                if action =="change_username":
                    return change_username(request.get_json())
                elif action=="change_profile":
                    return change_profile(request.get_json())
                elif action=='remove_collection':
                    return remove_favorite(request.get_json())
        else:
            return jsonify({"error": "无权访问该用户数据"}), 403
    except Exception:
        return jsonify({"error":"user doesn't exisit"}),404

@app.route("/api/admin/users/search", methods=["POST"])
@token_required
@levels_required('admin')
def user_search():
    try:
        data=request.get_json()
        # 尝试将 data 转为整数（用于 UserID 匹配）
        user_id = int(data)
    except ValueError:
        user_id = None  # 如果 data 不是数字，则只按用户名搜索

    # 构建查询条件：UserID=data 或 UserName包含data
    query = Users.query
    if user_id is not None:
        query = query.filter(
            (Users.UserID == user_id) |
            (Users.UserName.like(f'%{data}%'))
        )
    else:
        query = query.filter(Users.UserName.like(f'%{data}%'))

    # 执行查询并返回指定字段
    users = query.with_entities(
        Users.UserID,
        Users.UserName,
        Users.Level,
        Users.Status,
        Users.RegistrationTime
    ).all()

    # 格式化结果
    result = [{
        "id": u.UserID,
        "username": u.UserName,
        "type": u.Level,
        "status": u.Status,
        "joinDate": u.RegistrationTime.strftime('%Y-%m-%d')  # 格式化日期
    } for u in users]

    return jsonify(result)

@app.route("/api/admin/users/status", methods=["POST"])
@token_required
@levels_required('admin')
def status_change():
    data=request.get_json()
    username = data.get('username')
    new_status = data.get('status')

    if not username or not new_status:
        return jsonify({"error": "username 和 newStatus 不能为空"}), 400

    valid_statuses = ['active', 'banned']
    if new_status not in valid_statuses:
        return jsonify({"error": "无效的状态值"}), 400

    # 查询并更新
    user = Users.query.filter_by(UserName=username).first()
    if not user:
        return jsonify({"error": "用户不存在"}), 404


    return jsonify({
        "message": "状态更新成功",
        "username": username,
        "newStatus": new_status
    }), 200

@app.route("/api/answers/<int:question_id>", methods=["GET"])
def get_answers(question_id):
    try:
        answers = Answers.query.filter_by(
            QuesID=question_id
        ).with_entities(
            Answers.AnsID,
            Answers.AnsTitle,
            Answers.Content,
            Answers.TIME,
            Answers.UserID
        ).all()

        if not answers:
            return jsonify({"message": "该问题暂无答案"}),404

        result = [{
            "id": ans.AnsID,
            "AnsTitle": ans.AnsTitle,
            "Content": ans.Content,
            "TIME": ans.TIME.strftime('%Y-%m-%d %H:%M:%S'),
            "UserID": ans.UserID
        } for ans in answers]

        return jsonify(result), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/user/<string:userId>/submissions', methods=['GET'])
def get_user_sub(userId):
    page = request.args.get('page', default=1, type=int)
    per_page = request.args.get('per_page', default=10, type=int)

    # 查询 Submissions 表，并关联 Questions 表
    submissions_query = (
        db.session.query(
            Submissions.SubID,
            Questions.QuesName,  # 从 Questions 表获取题目名称
            Submissions.SubTime,
            Submissions.PassStatus
        )
        .join(Questions, Submissions.QuesID == Questions.QuesID)  # 关联 Questions 表
        .filter(Submissions.UserID == userId)  # 筛选指定用户
        .order_by(Submissions.SubTime.desc())  # 按提交时间降序排列
    )

    # 执行分页查询
    paginated_submissions = submissions_query.paginate(
        page=page,
        per_page=per_page,
        error_out=False  # 如果页码超出范围，返回空列表而不是 404
    )

    # 构造返回数据
    submissions_data = []
    for submission in paginated_submissions.items:
        submissions_data.append({
            "id": submission.SubID,
            "problem_title": submission.QuesName,
            "submit_time": submission.SubTime.isoformat(),
            "status": submission.PassStatus
        })

    return jsonify({
        "submissions": submissions_data,
        "total": paginated_submissions.total,
    })

@app.route('/api/solutions', methods=['POST'])
def manage_solutions():
    action=request.args.get('action')
    if action=='get_solutions':
        return get_solutions(request.get_json())
    elif action=='update_solutions':
        return update_solutions(request.get_json())
    elif action=='remove_solutions':
        return remove_solutions(request.get_json())
    elif action=='update_solution_status':
        return update_solution_status(request.get_json())

@app.route('/api/admin/comments', methods=['POST'])
def manage_rwfb():
    action=request.args.get('action')
    if action=='get_rwfb':
        return get_rwfb(request.get_json())
    elif action=='remove_rwfb':
        return remove_rwfb(request.get_json())
    elif action=='remove_rwfbs':
        return remove_rwfbs(request.get_json())

@app.route('/api/submission/<int:submission_id>/status', methods=['GET'])
def get_submission_status(submission_id):
    """获取提交状态"""
    try:
        submission = Submissions.query.get(submission_id)
        if not submission:
            return jsonify({"error": "提交不存在"}), 404

        return jsonify({
            "submission_id": submission.SubID,
            "status": submission.PassStatus,
            "message": submission.JudgeResult,
            "language": submission.Language,
            "submit_time": submission.SubTime.isoformat() if submission.SubTime else None,
            "execution_time": submission.ExecutionTime,
            "memory_usage": submission.MemoryUsage
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500



def generate_token(user_id, username, level):
    """生成JWT Token"""
    try:
        payload = {
            'exp': datetime.utcnow() + app.config['JWT_EXPIRATION_DELTA'],
            'iat': datetime.utcnow(),
            'sub': user_id,
            'username': username,
            'level': level
        }
        return jwt.encode(
            payload,
            app.config['SECRET_KEY'],
            algorithm='HS256'
        )
    except Exception as e:
        return e

def verify_token(token):
    """验证JWT Token"""
    try:
        payload = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
        return payload
    except jwt.ExpiredSignatureError:
        return 'Token已过期'
    except jwt.InvalidTokenError:
        return '无效Token'

@app.route("/api/test", methods=["GET"])
def test_endpoint():
    """简单的测试端点"""
    return jsonify({
        "status": "success",
        "message": "API 服务器正常运行"
    })

@app.route("/api/test/db", methods=["GET"])
def test_db_endpoint():
    """测试数据库连接"""
    try:
        count = Questions.query.count()
        questions = Questions.query.limit(3).all()

        result = {
            "status": "success",
            "message": f"数据库连接正常，共有 {count} 道题目",
            "sample_questions": [
                {
                    "id": q.QuesID,
                    "title": q.QuesName,
                    "difficulty": q.QuesLevel,
                    "tags": q.QuesType
                } for q in questions
            ]
        }
        return jsonify(result)
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"数据库连接失败: {str(e)}"
        }), 500



if __name__=='__main__':
    # 启动判题服务
    from judge import get_judge_service
    judge_service = get_judge_service()
    judge_service.start()

    try:
        app.run(host="127.0.0.1", port=5000, debug=True)
    finally:
        # 停止判题服务
        judge_service.stop()

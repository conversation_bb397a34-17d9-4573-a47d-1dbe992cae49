from models import *
from database import db
from sqlalchemy import func, case
from sqlalchemy.sql import label
from flask import jsonify, request
from datetime import datetime, timezone


def get_rank():
    try:
        # 统计每个用户的通过题目数、总提交数和通过率
        leaderboard_query = db.session.query(
            Users.UserID,
            Users.UserName,
            label('solved_count', func.count(
                case([(Submissions.PassStatus == 'passed', Submissions.QuesID)],
                distinct=True)
                )
            ),
            label('total_attempts', func.count(Submissions.SubID)),
            label('pass_rate',
                func.round(
                    func.count(case([(Submissions.PassStatus == 'passed', 1)])) * 100.0 /
                    func.nullif(func.count(Submissions.SubID), 0),
                    2
                )
            )
        ).join(
            Submissions, Users.UserID == Submissions.UserID
        ).group_by(
            Users.UserID, Users.UserName
        ).order_by(
            func.count(
                case([(Submissions.PassStatus == 'passed', Submissions.QuesID)],
                distinct=True)
            ).desc()
        ).limit(100)

        # 添加排名
        ranked_results = []
        for rank, (username, solved, rate) in enumerate(leaderboard_query, start=1):
            ranked_results.append({"user":{
                'rank': rank,
                'username': username,
                'solved_count': solved,
                'pass_rate': float(rate) if rate else 0.0
            }})

        return jsonify(ranked_results), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

def get_userinfo(userid):
    try:
        user = Users.query.get_or_404(userid)

        # 简化查询，分别计算统计数据
        # 计算总提交数
        total_submissions = db.session.query(func.count(Submissions.SubID)).filter(
            Submissions.UserID == userid
        ).scalar() or 0

        # 计算通过的题目数（去重）
        solved_count = db.session.query(func.count(func.distinct(Submissions.QuesID))).filter(
            Submissions.UserID == userid,
            Submissions.PassStatus == 'passed'
        ).scalar() or 0

        # 计算通过率
        if total_submissions > 0:
            passed_submissions = db.session.query(func.count(Submissions.SubID)).filter(
                Submissions.UserID == userid,
                Submissions.PassStatus == 'passed'
            ).scalar() or 0
            accuracy = round((passed_submissions / total_submissions) * 100, 2)
        else:
            accuracy = 0.0

        return jsonify({
            "username": user.UserName,
            "joinDate": user.RegistrationTime.strftime('%Y-%m-%d') if user.RegistrationTime else "未知",
            "avatar": user.UserPict or "https://via.placeholder.com/100",
            "solved": solved_count,
            "submitted": total_submissions,
            "accuracy": accuracy
        })

    except Exception as e:
        print(f"获取用户信息时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

def change_username(data):
    try:
        userid=data.get('userid')
        new_name=data.get('username')
        user=Users.query.get(userid)
        if user:
            user.UserName=new_name
        db.session.commit()
        return jsonify({"message":"change user name successfully"})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def change_profile(data):
    try:
        userid=data.get('userid')
        new_profile=data.get('avatar')
        user=Users.query.get(userid)
        if user:
            user.UserPict=new_profile
        db.session.commit()
        return jsonify({"message":"change user profile successfully"})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def check_favorite(data):
    ques_id=data.get('problem_id')
    user_id=data.get('user_id')
    collection = Collections.query.filter_by(
        QuesID=ques_id,
        UserID=user_id
    ).first()

    return jsonify({"is_favorite": collection is not None}), 200

def remove_favorite(data):
    ques_id=data.get('problem_id')
    user_id=data.get('user_id')
    collection = Collections.query.filter_by(
        QuesID=ques_id,
        UserID=user_id
    ).first()

    if not collection:
        return jsonify({"error": "收藏不存在"}), 404

    db.session.delete(collection)
    db.session.commit()
    return jsonify({"message": "移除收藏成功"}), 200

def add_favorite(data):
    ques_id=data.get('problem_id')
    user_id=data.get('user_id')

    # 检查是否已经收藏
    existing = Collections.query.filter_by(
        QuesID=ques_id,
        UserID=user_id
    ).first()

    if existing:
        return jsonify({"error": "已经收藏过此题目"}), 400

    # 创建新记录
    new_collection = Collections(
        QuesID=ques_id,
        UserID=user_id,
        QuesRemark="",  # 可选备注
        CollTime=datetime.now(timezone.utc)  # 自动设置当前时间
    )

    db.session.add(new_collection)
    db.session.commit()
    return jsonify({"message": "收藏成功"}), 201

def get_collections(id):
    collections = Collections.query.filter_by(
        UserID=id
    ).join(  # 通过 relationship 关联 Questions 表
        Questions, Collections.QuesID == Questions.QuesID
    ).with_entities(  # 指定返回字段
        Collections.QuesID,
        Questions.QuesName,  # 来自 Questions 表
        Collections.CollTime,
        Collections.QuesRemark  # 可选备注
    ).all()

    # 格式化结果
    result = [{
        "problem_id": col.QuesID,
        "title": col.QuesName,
        "added_at": col.CollTime.strftime('%Y-%m-%d'),  # 格式化时间
    } for col in collections]

    return jsonify(result)

def get_solutions(data):
    pg=data.get('pg')
    ppg=data.get('ppg')
    search=data.get('search')
    status=data.get('status')

 # 构建基础查询（关联 Answers 和 Questions 表）
    query = (
        db.session.query(
            Answers.AnsID.label('id'),
            Answers.AnsTitle,
            Questions.QuesName.label('ProblemTitle'),  # 别名映射到前端字段
            Answers.UserID,
            Answers.AnsStatus.label('Status'),        # 别名映射到前端字段
            Answers.TIME
        )
        .join(Questions, Answers.QuesID == Questions.QuesID)
    )

    # 添加筛选条件
    if search:
        query = query.filter(Questions.QuesName.ilike(f'%{search}%'))  # 模糊搜索 QuesName
    if status:
        query = query.filter(Answers.AnsStatus == status)  # 按状态筛选

    # 按时间降序排列并分页
    paginated_answers = query.order_by(Answers.TIME.desc()).paginate(
        page=pg,
        per_page=ppg,
        error_out=False
    )

    # 构造返回数据
    answers_data = []
    for answer in paginated_answers.items:
        answers_data.append({
            "id": answer.id,
            "AnsTitle": answer.AnsTitle,
            "ProblemTitle": answer.ProblemTitle,
            "UserID": answer.UserID,
            "Status": answer.Status,
            "TIME": answer.TIME.isoformat()  # 格式化时间为字符串
        })

    return jsonify({
        "answers": answers_data,
        "total": paginated_answers.total
    })

def update_solutions(data):
    data = request.get_json()

    # 提取数据（确保字段匹配）
    answer_data = {
        "AnsID": data.get("id"),  # 主键，null 表示新记录
        "AnsTitle": data.get("AnsTitle"),
        "QuesID": data.get("ProblemID"),  # 注意字段名映射
        "Content": data.get("Content", ""),
        "AnsStatus": data.get("Status", "draft"),  # 注意字段名映射
        "UserID": data.get("UserID", ""),
        "TIME": datetime.now(timezone.utc)  # 自动设置当前时间
    }

    # 创建 Answer 对象
    answer = Answers(**answer_data)

    try:
        # 判断是插入还是更新
        if answer.AnsID is None:
            # 新记录：插入（自动生成 ID）
            db.session.add(answer)
        else:
            # 更新记录：使用 merge 合并变更
            db.session.merge(answer)

        db.session.commit()
        return jsonify({
            "success": True,
            "id": answer.AnsID  # 返回新生成的 ID 或更新的 ID
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500

def update_solution_status(data):
    ans_id=data.get('id')
    new_status=data.get('status')
    updated_rows = Answers.query.filter_by(AnsID=ans_id).update(
        {"AnsStatus": new_status}
    )
    db.session.commit()

def remove_solutions(data):
    ans_id=data.get('ans_id')
    answer = Answers.query.get(ans_id)
    if not answer:
        return False, "答案不存在"

    # 执行删除
    db.session.delete(answer)
    db.session.commit()
    return True, "删除成功"

def get_rwfb():
    data=request.get_json()
    pg=data.get("page",1)
    ppg=data.get("per_page",15)
    start_date = datetime.strptime(data['start_date'], '%Y-%m-%d')
    end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').replace(hour=23, minute=59, second=59)
    search=data.get("search").strip()

    query = ReviewsFeedback.query \
            .join(Reviews, ReviewsFeedback.ReviID == Reviews.ReviID) \
            .join(Questions, Reviews.QuesID == Questions.QuesID) \
            .filter(ReviewsFeedback.FbTime.between(start_date, end_date))

    # 添加搜索条件
    if search:
            try:
                # 尝试按题目ID搜索（如果搜索词是数字）
                ques_id = int(search)
                query = query.filter(Questions.QuesID == ques_id)
            except ValueError:
                # 按题目名称模糊搜索
                query = query.filter(Questions.QuesName.ilike(f'%{search}%'))

        # 执行分页查询
    paginated = query.paginate(page=pg, per_page=ppg, error_out=False)

        # 构建响应数据
    feedbacks = [{
            'id': fb.ReviFbID,
            'targetTitle': fb.review.question.QuesName,
            'content': fb.FbCont,
            'createdAt': fb.FbTime.isoformat(),
            'user_id': fb.UserName
        } for fb in paginated.items]

    return jsonify({
            'comments': feedbacks,
            'total': paginated.total,
            'current_page': paginated.page,
            'per_page': paginated.per_page
        }),200

def remove_rwfbs(data):
    comment_ids = data.get('comment_ids', [])  # 例如 [1, 2, 3]

    if not comment_ids:
        return jsonify({'error': 'No comment IDs provided'}), 400

    try:
        # 批量删除符合条件的记录
        delete_count = ReviewsFeedback.query.filter(
            ReviewsFeedback.ReviID.in_(comment_ids)
        ).delete(synchronize_session=False)

        db.session.commit()
        return jsonify({
            'message': f'Successfully deleted {delete_count} feedback(s)',
            'deleted_ids': comment_ids
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def remove_rwfb(data):
    id=data.get('id')
    rwfb=ReviewsFeedback.query.get(id)
    db.session.delete(rwfb)
    db.session.commit()